export enum RoleType {
  SUPER_ADMIN = 'super_admin',
  MANAGER = 'manager',
  TEAM_LEADER = 'team_leader',
  TEAM_MEMBER = 'team_member'
}

export const ROLE_DESCRIPTIONS = {
  [RoleType.SUPER_ADMIN]: 'Super Administrator with full system access',
  [RoleType.MANAGER]: 'Manager with administrative privileges',
  [RoleType.TEAM_LEADER]: 'Team Leader with team management access',
  [RoleType.TEAM_MEMBER]: 'Team member with limited access'
};
