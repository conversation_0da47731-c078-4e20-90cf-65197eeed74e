export enum PermissionsType {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  MANAGE = 'manage',
  APPROVE = 'approve',
  EXPORT = 'export',
  IMPORT = 'import',
  ANALYTICS = 'analytics',
  PRINT = 'print'
}

export const PERMISSION_DESCRIPTIONS = {
  [PermissionsType.CREATE]: 'Create new records',
  [PermissionsType.READ]: 'Read existing records',
  [PermissionsType.UPDATE]: 'Update existing records',
  [PermissionsType.DELETE]: 'Delete records',
  [PermissionsType.VIEW]: 'View records and data',
  [PermissionsType.MANAGE]: 'Full management access',
  [PermissionsType.APPROVE]: 'Approve operations',
  [PermissionsType.EXPORT]: 'Export data',
  [PermissionsType.IMPORT]: 'Import data',
};

export const PERMISSION_GROUPS = {
  BASIC: [PermissionsType.VIEW, PermissionsType.READ],
  STANDARD: [PermissionsType.VIEW, PermissionsType.READ, PermissionsType.CREATE, PermissionsType.UPDATE],
  ADVANCED: [PermissionsType.VIEW, PermissionsType.READ, PermissionsType.CREATE, PermissionsType.UPDATE, PermissionsType.DELETE],
  ADMIN: Object.values(PermissionsType),
};
