import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CustomLoggerService } from './logger.service';

@Injectable()
export class HttpLoggingInterceptor implements NestInterceptor {
  constructor(private readonly logger: CustomLoggerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const { method, url, headers, body, query, params } = request;
    const userAgent = headers['user-agent'] || '';
    const ip = headers['x-forwarded-for'] || headers['x-real-ip'] || request.connection.remoteAddress || request.ip;
    
    const startTime = Date.now();

    // Log request
    this.logger.logWithMeta('http', `Incoming Request: ${method} ${url}`, {
      method,
      url,
      userAgent,
      ip,
      query,
      params,
      body: this.sanitizeBody(body),
      type: 'incoming_request',
    }, 'HTTP');

    return next.handle().pipe(
      tap({
        next: (data) => {
          const duration = Date.now() - startTime;
          const { statusCode } = response;

          // Log successful response
          this.logger.logRequest(method, url, statusCode, duration, userAgent, ip);
          
          // Log response details for debugging (only in development)
          if (process.env.NODE_ENV === 'development') {
            this.logger.logWithMeta('debug', `Response: ${method} ${url}`, {
              statusCode,
              duration,
              responseSize: data ? JSON.stringify(data).length : 0,
              type: 'response',
            }, 'HTTP');
          }
        },
        error: (error) => {
          const duration = Date.now() - startTime;
          const statusCode = error.status || 500;

          // Log error response
          this.logger.logRequest(method, url, statusCode, duration, userAgent, ip);
          
          // Log error details
          this.logger.logWithMeta('error', `Error Response: ${method} ${url}`, {
            statusCode,
            duration,
            error: error.message,
            stack: error.stack,
            type: 'error_response',
          }, 'HTTP');
        },
      }),
    );
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***REDACTED***';
      }
    }

    return sanitized;
  }
}
