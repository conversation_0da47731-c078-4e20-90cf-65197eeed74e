import { Inject } from '@nestjs/common';
import { CustomLoggerService } from './logger.service';

export const InjectLogger = () => Inject(CustomLoggerService);

// Method decorator for automatic logging
export function LogMethod(context?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const logger: CustomLoggerService = this.logger || this.loggerService;
      const className = target.constructor.name;
      const methodContext = context || `${className}.${propertyName}`;
      
      const startTime = Date.now();
      
      try {
        logger?.debug(`Method ${propertyName} started`, methodContext);
        
        const result = await method.apply(this, args);
        
        const duration = Date.now() - startTime;
        logger?.logPerformance(`${className}.${propertyName}`, duration, methodContext);
        
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        logger?.error(
          `Method ${propertyName} failed after ${duration}ms: ${error.message}`,
          error.stack,
          methodContext,
        );
        throw error;
      }
    };

    return descriptor;
  };
}

// Class decorator for automatic logger injection
export function WithLogger(context?: string) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    return class extends constructor {
      public readonly loggerContext = context || constructor.name;
      
      constructor(...args: any[]) {
        super(...args);
      }
    };
  };
}
