import { Injectable } from '@nestjs/common';
import { CustomLoggerService } from './logger.service';
import { LogMethod, InjectLogger } from './logger.decorator';

/**
 * Example service showing how to use the custom logger
 */
@Injectable()
export class ExampleService {
  constructor(
    @InjectLogger() private readonly logger: CustomLoggerService,
  ) {}

  // Basic logging examples
  basicLoggingExamples() {
    // Basic log levels
    this.logger.log('This is an info message', 'ExampleService');
    this.logger.error('This is an error message', 'Error stack trace', 'ExampleService');
    this.logger.warn('This is a warning message', 'ExampleService');
    this.logger.debug('This is a debug message', 'ExampleService');
    this.logger.verbose('This is a verbose message', 'ExampleService');
  }

  // Structured logging examples
  structuredLoggingExamples() {
    // Log with metadata
    this.logger.logWithMeta('info', 'User action performed', {
      userId: '123',
      action: 'create_post',
      timestamp: new Date().toISOString(),
      metadata: { postId: '456', title: 'Sample Post' },
    }, 'UserService');

    // Performance logging
    this.logger.logPerformance('database_query', 150, 'DatabaseService');

    // Authentication logging
    this.logger.logAuth('login', 'john_doe', true, '192.168.1.1', 'Mozilla/5.0...');
    this.logger.logAuth('login', 'jane_doe', false, '192.168.1.2', 'Chrome/91.0...');

    // Database operation logging
    this.logger.logDatabase('SELECT', 'users', 45);
    this.logger.logDatabase('INSERT', 'posts', undefined, 'Duplicate key error');

    // Security logging
    this.logger.logSecurity('failed_login_attempt', 'medium', {
      username: 'admin',
      attempts: 3,
      timeWindow: '5 minutes',
    }, '192.168.1.100');

    this.logger.logSecurity('sql_injection_attempt', 'high', {
      query: 'SELECT * FROM users WHERE id = 1; DROP TABLE users;',
      endpoint: '/api/users',
      blocked: true,
    }, '10.0.0.1');
  }

  // Method with automatic logging using decorator
  @LogMethod('ExampleService')
  async methodWithLogging(param1: string, param2: number): Promise<string> {
    // Simulate some work
    await new Promise(resolve => setTimeout(resolve, 100));
    
    this.logger.info(`Processing with params: ${param1}, ${param2}`, 'ExampleService');
    
    if (param2 < 0) {
      throw new Error('Negative numbers not allowed');
    }
    
    return `Processed: ${param1} - ${param2}`;
  }

  // HTTP request logging example
  logHttpRequest() {
    this.logger.logRequest(
      'POST',
      '/api/users',
      201,
      250,
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      '192.168.1.50'
    );
  }

  // Error handling with logging
  async errorHandlingExample() {
    try {
      // Simulate an operation that might fail
      throw new Error('Something went wrong');
    } catch (error) {
      this.logger.error(
        `Operation failed: ${error.message}`,
        error.stack,
        'ExampleService'
      );
      
      // Log additional context
      this.logger.logWithMeta('error', 'Operation context', {
        operation: 'data_processing',
        input: { type: 'user_data', size: 1024 },
        timestamp: new Date().toISOString(),
        errorCode: 'PROC_001',
      }, 'ExampleService');
      
      throw error; // Re-throw if needed
    }
  }

  // Business logic logging
  async businessLogicExample(userId: string, action: string) {
    const startTime = Date.now();
    
    try {
      this.logger.info(`Starting ${action} for user ${userId}`, 'BusinessLogic');
      
      // Simulate business logic
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const duration = Date.now() - startTime;
      
      // Log successful completion
      this.logger.logWithMeta('info', `${action} completed successfully`, {
        userId,
        action,
        duration,
        status: 'success',
      }, 'BusinessLogic');
      
      return { success: true, duration };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Log failure
      this.logger.logWithMeta('error', `${action} failed`, {
        userId,
        action,
        duration,
        status: 'failed',
        error: error.message,
      }, 'BusinessLogic');
      
      throw error;
    }
  }
}

/**
 * Usage examples in different scenarios
 */
export class LoggerUsageExamples {
  
  // Controller logging example
  static controllerExample(logger: CustomLoggerService) {
    // Log incoming request details
    logger.logWithMeta('http', 'Incoming request', {
      method: 'POST',
      url: '/api/users',
      userAgent: 'PostmanRuntime/7.28.0',
      ip: '127.0.0.1',
    }, 'UsersController');
    
    // Log validation errors
    logger.logWithMeta('warn', 'Validation failed', {
      errors: ['email is required', 'password too short'],
      endpoint: '/api/users',
    }, 'ValidationPipe');
  }
  
  // Service logging example
  static serviceExample(logger: CustomLoggerService) {
    // Log service operations
    logger.info('Creating new user', 'UsersService');
    
    // Log with business context
    logger.logWithMeta('info', 'User created successfully', {
      userId: 'user_123',
      email: '<EMAIL>',
      roles: ['user'],
      createdAt: new Date().toISOString(),
    }, 'UsersService');
  }
  
  // Repository/Database logging example
  static repositoryExample(logger: CustomLoggerService) {
    // Log database operations
    logger.logDatabase('INSERT', 'users', 45);
    logger.logDatabase('UPDATE', 'user_profiles', 32);
    logger.logDatabase('SELECT', 'posts', 120);
    
    // Log database errors
    logger.logDatabase('INSERT', 'users', undefined, 'Duplicate entry for key email');
  }
  
  // Middleware logging example
  static middlewareExample(logger: CustomLoggerService) {
    // Log middleware execution
    logger.debug('Authentication middleware executed', 'AuthMiddleware');
    
    // Log security events
    logger.logSecurity('unauthorized_access_attempt', 'medium', {
      endpoint: '/api/admin/users',
      token: 'invalid_token_hash',
      action: 'blocked',
    }, '192.168.1.100');
  }
}
