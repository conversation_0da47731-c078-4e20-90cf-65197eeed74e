import { Injectable, LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

@Injectable()
export class CustomLoggerService implements LoggerService {
  private logger: winston.Logger;

  constructor(private configService: ConfigService) {
    this.createLogger();
  }

  private createLogger() {
    const logLevel = this.configService.get('logging.level', 'info');
    const fileEnabled = this.configService.get('logging.fileEnabled', true);
    const consoleEnabled = this.configService.get('logging.consoleEnabled', true);
    const maxFiles = this.configService.get('logging.maxFiles', '5');
    const maxSize = this.configService.get('logging.maxSize', '10m');

    const transports: winston.transport[] = [];

    // Console transport
    if (consoleEnabled) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
            winston.format.colorize({ all: true }),
            winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
              const contextStr = context ? `[${context}] ` : '';
              const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
              const traceStr = trace ? `\n${trace}` : '';
              return `${timestamp} ${level}: ${contextStr}${message}${metaStr}${traceStr}`;
            }),
          ),
        }),
      );
    }

    // File transports
    if (fileEnabled) {
      // Error logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          maxFiles,
          maxSize,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.errors({ stack: true }),
            winston.format.json(),
          ),
        }),
      );

      // Combined logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/combined-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxFiles,
          maxSize,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.errors({ stack: true }),
            winston.format.json(),
          ),
        }),
      );

      // Access logs
      transports.push(
        new DailyRotateFile({
          filename: 'logs/access-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'http',
          maxFiles,
          maxSize,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
      );
    }

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
      ),
      transports,
      exceptionHandlers: fileEnabled
        ? [
            new DailyRotateFile({
              filename: 'logs/exceptions-%DATE%.log',
              datePattern: 'YYYY-MM-DD',
              maxFiles,
              maxSize,
            }),
          ]
        : [],
      rejectionHandlers: fileEnabled
        ? [
            new DailyRotateFile({
              filename: 'logs/rejections-%DATE%.log',
              datePattern: 'YYYY-MM-DD',
              maxFiles,
              maxSize,
            }),
          ]
        : [],
    });
  }

  log(message: any, context?: string) {
    this.logger.info(message, { context });
  }

  error(message: any, trace?: string, context?: string) {
    this.logger.error(message, { context, trace });
  }

  warn(message: any, context?: string) {
    this.logger.warn(message, { context });
  }

  debug(message: any, context?: string) {
    this.logger.debug(message, { context });
  }

  verbose(message: any, context?: string) {
    this.logger.verbose(message, { context });
  }

  // Additional methods for different log levels
  info(message: any, context?: string, meta?: any) {
    this.logger.info(message, { context, ...meta });
  }

  http(message: any, context?: string, meta?: any) {
    this.logger.http(message, { context, ...meta });
  }

  // Method for structured logging
  logWithMeta(level: string, message: string, meta: any, context?: string) {
    this.logger.log(level, message, { context, ...meta });
  }

  // Method for performance logging
  logPerformance(operation: string, duration: number, context?: string) {
    this.logger.info(`Performance: ${operation} completed in ${duration}ms`, {
      context: context || 'Performance',
      operation,
      duration,
      type: 'performance',
    });
  }

  // Method for API request logging
  logRequest(method: string, url: string, statusCode: number, duration: number, userAgent?: string, ip?: string) {
    this.logger.http(`${method} ${url} ${statusCode} - ${duration}ms`, {
      context: 'HTTP',
      method,
      url,
      statusCode,
      duration,
      userAgent,
      ip,
      type: 'request',
    });
  }

  // Method for authentication logging
  logAuth(action: string, username: string, success: boolean, ip?: string, userAgent?: string) {
    const level = success ? 'info' : 'warn';
    this.logger.log(level, `Auth: ${action} - ${username} - ${success ? 'SUCCESS' : 'FAILED'}`, {
      context: 'Auth',
      action,
      username,
      success,
      ip,
      userAgent,
      type: 'authentication',
    });
  }

  // Method for database operation logging
  logDatabase(operation: string, table: string, duration?: number, error?: string) {
    const level = error ? 'error' : 'debug';
    const message = error 
      ? `Database Error: ${operation} on ${table} - ${error}`
      : `Database: ${operation} on ${table}${duration ? ` - ${duration}ms` : ''}`;
    
    this.logger.log(level, message, {
      context: 'Database',
      operation,
      table,
      duration,
      error,
      type: 'database',
    });
  }

  // Method for security logging
  logSecurity(event: string, severity: 'low' | 'medium' | 'high' | 'critical', details: any, ip?: string) {
    const level = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
    this.logger.log(level, `Security: ${event} - Severity: ${severity}`, {
      context: 'Security',
      event,
      severity,
      details,
      ip,
      type: 'security',
    });
  }
}
