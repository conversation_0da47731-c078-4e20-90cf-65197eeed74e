import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../../entities/user.entity';
import { Role } from '../../entities/role.entity';
import { UserContextService } from './user-context.service';

@Module({
  imports: [TypeOrmModule.forFeature([User, Role])],
  providers: [UserContextService],
  exports: [UserContextService],
})
export class CommonServicesModule {}
