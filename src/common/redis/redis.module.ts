import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: async (configService: ConfigService) => {
        const Redis = require('ioredis');
        
        const redisConfig = {
          host: configService.get('redis.host'),
          port: configService.get('redis.port'),
          password: configService.get('redis.password') || undefined,
          db: configService.get('redis.db'),
          maxRetriesPerRequest: configService.get('redis.maxRetriesPerRequest'),
          retryDelayOnFailover: configService.get('redis.retryDelayOnFailover'),
          enableReadyCheck: configService.get('redis.enableReadyCheck'),
          lazyConnect: configService.get('redis.lazyConnect'),
        };

        // Remove undefined values
        Object.keys(redisConfig).forEach(key => {
          if (redisConfig[key] === undefined) {
            delete redisConfig[key];
          }
        });

        const redis = new Redis(redisConfig);

        redis.on('connect', () => {
          console.log('Redis connected successfully');
        });

        redis.on('error', (error) => {
          console.error('Redis connection error:', error);
        });

        redis.on('ready', () => {
          console.log('Redis is ready to receive commands');
        });

        return redis;
      },
      inject: [ConfigService],
    },
    RedisService,
  ],
  exports: ['REDIS_CLIENT', RedisService],
})
export class RedisModule {}
