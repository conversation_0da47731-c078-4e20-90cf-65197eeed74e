/**
 * Utility functions for integrating logger across all services
 * This file contains helper functions to standardize logging across the application
 */

import { CustomLoggerService } from '../logger/logger.service';

export interface LoggerIntegrationOptions {
  context: string;
  operation: string;
  data?: any;
  userId?: string;
  duration?: number;
}

/**
 * Standard logging patterns for common operations
 */
export class LoggerIntegrationUtil {
  /**
   * Log service operation start
   */
  static logOperationStart(
    logger: CustomLoggerService,
    context: string,
    operation: string,
    data?: any
  ): void {
    logger.info(`${operation} started`, context, data);
  }

  /**
   * Log service operation success
   */
  static logOperationSuccess(
    logger: CustomLoggerService,
    context: string,
    operation: string,
    data?: any,
    duration?: number
  ): void {
    const message = duration 
      ? `${operation} completed successfully in ${duration}ms`
      : `${operation} completed successfully`;
    
    logger.info(message, context, data);
  }

  /**
   * Log service operation error
   */
  static logOperationError(
    logger: CustomLoggerService,
    context: string,
    operation: string,
    error: Error,
    data?: any
  ): void {
    logger.error(`${operation} failed: ${error.message}`, error.stack, context);
  }

  /**
   * Log validation error
   */
  static logValidationError(
    logger: CustomLoggerService,
    context: string,
    operation: string,
    validationMessage: string,
    data?: any
  ): void {
    logger.warn(`${operation} validation failed: ${validationMessage}`, context);
  }

  /**
   * Log not found error
   */
  static logNotFound(
    logger: CustomLoggerService,
    context: string,
    resource: string,
    identifier: string
  ): void {
    logger.warn(`${resource} not found: ${identifier}`, context);
  }

  /**
   * Log conflict error
   */
  static logConflict(
    logger: CustomLoggerService,
    context: string,
    operation: string,
    conflictMessage: string
  ): void {
    logger.warn(`${operation} conflict: ${conflictMessage}`, context);
  }

  /**
   * Log database operation
   */
  static logDatabaseOperation(
    logger: CustomLoggerService,
    context: string,
    operation: string,
    table: string,
    duration?: number,
    error?: string
  ): void {
    logger.logDatabase(operation, table, duration, error);
  }

  /**
   * Log authentication event
   */
  static logAuthEvent(
    logger: CustomLoggerService,
    action: string,
    username: string,
    success: boolean,
    ip?: string,
    userAgent?: string
  ): void {
    logger.logAuth(action, username, success, ip, userAgent);
  }

  /**
   * Log security event
   */
  static logSecurityEvent(
    logger: CustomLoggerService,
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details: any,
    ip?: string
  ): void {
    logger.logSecurity(event, severity, details, ip);
  }

  /**
   * Log performance metrics
   */
  static logPerformance(
    logger: CustomLoggerService,
    operation: string,
    duration: number,
    context?: string
  ): void {
    logger.logPerformance(operation, duration, context);
  }

  /**
   * Create a timing decorator for methods
   */
  static createTimingDecorator(logger: CustomLoggerService, context: string) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      const method = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        const startTime = Date.now();
        const operation = `${target.constructor.name}.${propertyName}`;

        LoggerIntegrationUtil.logOperationStart(logger, context, operation);

        try {
          const result = await method.apply(this, args);
          const duration = Date.now() - startTime;
          
          LoggerIntegrationUtil.logOperationSuccess(logger, context, operation, undefined, duration);
          LoggerIntegrationUtil.logPerformance(logger, operation, duration, context);
          
          return result;
        } catch (error) {
          const duration = Date.now() - startTime;
          LoggerIntegrationUtil.logOperationError(logger, context, operation, error);
          throw error;
        }
      };

      return descriptor;
    };
  }

  /**
   * Standardized error handling with logging
   */
  static async executeWithLogging<T>(
    logger: CustomLoggerService,
    context: string,
    operation: string,
    fn: () => Promise<T>,
    data?: any
  ): Promise<T> {
    const startTime = Date.now();
    
    LoggerIntegrationUtil.logOperationStart(logger, context, operation, data);

    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      LoggerIntegrationUtil.logOperationSuccess(logger, context, operation, data, duration);
      
      return result;
    } catch (error) {
      LoggerIntegrationUtil.logOperationError(logger, context, operation, error as Error, data);
      throw error;
    }
  }

  /**
   * Log HTTP request details
   */
  static logHttpRequest(
    logger: CustomLoggerService,
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    userAgent?: string,
    ip?: string
  ): void {
    logger.logRequest(method, url, statusCode, duration, userAgent, ip);
  }

  /**
   * Log with structured metadata
   */
  static logWithMetadata(
    logger: CustomLoggerService,
    level: 'info' | 'warn' | 'error' | 'debug',
    message: string,
    context: string,
    metadata?: any
  ): void {
    switch (level) {
      case 'info':
        logger.info(message, context, metadata);
        break;
      case 'warn':
        logger.warn(message, context);
        break;
      case 'error':
        logger.error(message, undefined, context);
        break;
      case 'debug':
        logger.debug(message, context);
        break;
    }
  }

  /**
   * Create standardized service logger methods
   */
  static createServiceLogger(logger: CustomLoggerService, serviceName: string) {
    return {
      logCreate: (resourceName: string, data?: any) => {
        LoggerIntegrationUtil.logOperationStart(logger, serviceName, `create ${resourceName}`, data);
      },
      logCreateSuccess: (resourceName: string, id: string, data?: any) => {
        LoggerIntegrationUtil.logOperationSuccess(logger, serviceName, `create ${resourceName}`, { id, ...data });
      },
      logCreateError: (resourceName: string, error: Error, data?: any) => {
        LoggerIntegrationUtil.logOperationError(logger, serviceName, `create ${resourceName}`, error, data);
      },
      logUpdate: (resourceName: string, id: string, data?: any) => {
        LoggerIntegrationUtil.logOperationStart(logger, serviceName, `update ${resourceName}`, { id, ...data });
      },
      logUpdateSuccess: (resourceName: string, id: string, data?: any) => {
        LoggerIntegrationUtil.logOperationSuccess(logger, serviceName, `update ${resourceName}`, { id, ...data });
      },
      logUpdateError: (resourceName: string, id: string, error: Error, data?: any) => {
        LoggerIntegrationUtil.logOperationError(logger, serviceName, `update ${resourceName}`, error, { id, ...data });
      },
      logDelete: (resourceName: string, id: string) => {
        LoggerIntegrationUtil.logOperationStart(logger, serviceName, `delete ${resourceName}`, { id });
      },
      logDeleteSuccess: (resourceName: string, id: string) => {
        LoggerIntegrationUtil.logOperationSuccess(logger, serviceName, `delete ${resourceName}`, { id });
      },
      logDeleteError: (resourceName: string, id: string, error: Error) => {
        LoggerIntegrationUtil.logOperationError(logger, serviceName, `delete ${resourceName}`, error, { id });
      },
      logFind: (resourceName: string, criteria?: any) => {
        LoggerIntegrationUtil.logOperationStart(logger, serviceName, `find ${resourceName}`, criteria);
      },
      logFindSuccess: (resourceName: string, count: number, criteria?: any) => {
        LoggerIntegrationUtil.logOperationSuccess(logger, serviceName, `find ${resourceName}`, { count, ...criteria });
      },
      logNotFound: (resourceName: string, identifier: string) => {
        LoggerIntegrationUtil.logNotFound(logger, serviceName, resourceName, identifier);
      },
      logConflict: (operation: string, message: string) => {
        LoggerIntegrationUtil.logConflict(logger, serviceName, operation, message);
      },
      logValidationError: (operation: string, message: string, data?: any) => {
        LoggerIntegrationUtil.logValidationError(logger, serviceName, operation, message, data);
      }
    };
  }
}
