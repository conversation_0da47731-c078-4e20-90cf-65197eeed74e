import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { RoleType } from '../constants/role.constant';
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
   
    const isSuperAdmin = requiredRoles.includes(RoleType.SUPER_ADMIN) && user.username === RoleType.SUPER_ADMIN || user.roles?.includes(RoleType.SUPER_ADMIN);

    if (isSuperAdmin) {
      return true;
    }

    return requiredRoles.some((role) => user.roles?.includes(role));
  }
} 