import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/entities/user.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class IPWhitelistGuard implements CanActivate {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly logger: CustomLoggerService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    if (!user || !user.id) {
      return true; // Let other guards handle authentication
    }

    // Get client IP address
    const clientIP = this.getClientIP(request);
    
    // Check if user has IP whitelist configured
    const whitelistIPs = user.whitelistIPs || [];

    if (!whitelistIPs || whitelistIPs.length === 0) {
      // No whitelist configured, allow access
      return true;
    }

    // Check if client IP is in whitelist
    const isWhitelisted = this.isIPInWhitelist(clientIP,whitelistIPs);
    
    if (!isWhitelisted) {
      this.logger.warn(
        `Access denied for user ${user.id} from IP ${clientIP}. IP not in whitelist: ${whitelistIPs.join(', ')}`,
        'IPWhitelistGuard'
      );
      throw new ForbiddenException('Access denied: IP address not in whitelist');
    }

    this.logger.debug(
      `Access granted for user ${user.id} from IP ${clientIP}`,
      'IPWhitelistGuard'
    );

    return true;
  }

  private getClientIP(request: any): string {
    // Try to get IP from various headers (for proxy/load balancer scenarios)
    const xForwardedFor = request.headers['x-forwarded-for'];
    const xRealIP = request.headers['x-real-ip'];
    const cfConnectingIP = request.headers['cf-connecting-ip']; // Cloudflare
    
    if (xForwardedFor) {
      // X-Forwarded-For can contain multiple IPs, take the first one
      return xForwardedFor.split(',')[0].trim();
    }
    
    if (xRealIP) {
      return xRealIP;
    }
    
    if (cfConnectingIP) {
      return cfConnectingIP;
    }
    
    // Fallback to connection remote address
    return request.connection?.remoteAddress || 
           request.socket?.remoteAddress || 
           request.ip || 
           '127.0.0.1';
  }

  private isIPInWhitelist(clientIP: string, whitelist: string[]): boolean {
    // Simple IP matching - in production, you might want to support CIDR notation
    // For now, we'll do exact matching and basic wildcard support
    
    for (const whitelistEntry of whitelist) {
      if (whitelistEntry === clientIP) {
        return true;
      }
      
      // Support for simple wildcard matching (e.g., 192.168.1.*)
      if (whitelistEntry.includes('*')) {
        const pattern = whitelistEntry.replace(/\*/g, '.*');
        const regex = new RegExp(`^${pattern}$`);
        if (regex.test(clientIP)) {
          return true;
        }
      }
      
      // TODO: Add CIDR notation support if needed
      // You can use libraries like 'ip-range-check' or 'netmask' for this
    }
    
    return false;
  }
}
