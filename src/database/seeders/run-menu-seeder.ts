import { DataSource } from 'typeorm';
import { MenuSeeder } from './menu.seeder';

// Import all entities to avoid relationship errors
import { User } from '../../entities/user.entity';
import { Department } from '../../entities/department.entity';
import { Team } from '../../entities/team.entity';
import { Role } from '../../entities/role.entity';
import { Permission } from '../../entities/permission.entity';
import { UserMenuAssignment } from '../../entities/user-menu-assignment.entity';
import { Menu } from '../../entities/menu.entity';
import { SubMenu } from '../../entities/submenu.entity';
// Affiliate entities removed

/**
 * <PERSON>u Seeder Runner
 * Runs only the menu seeder
 */
async function runMenuSeeder() {
  console.log('Starting menu seeding...');

  // Create DataSource configuration
  const dataSource = new DataSource({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'cms_db',
    entities: [
      User,
      Department,
      Team,
      Role,
      Permission,
      UserMenuAssignment,
      Menu,
      SubMenu,
    ],
    synchronize: true,
    logging: false,
  });

  try {
    // Initialize the data source
    await dataSource.initialize();
    console.log('Database connection established');

    // Run menu seeder
    await MenuSeeder.run(dataSource);

    console.log('Menu seeder completed successfully!');
  } catch (error) {
    console.error('Error running menu seeder:', error);
    process.exit(1);
  } finally {
    // Close the connection
    await dataSource.destroy();
    console.log('Database connection closed');
  }
}

// Run the menu seeder
runMenuSeeder();
