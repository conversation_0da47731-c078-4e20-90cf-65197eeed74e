import { DataSource } from 'typeorm';
import { MenuSeeder } from './menu.seeder';

// Import all entities to avoid relationship errors
import { User } from '../../entities/user.entity';
import { Department } from '../../entities/department.entity';
import { Team } from '../../entities/team.entity';
import { Role } from '../../entities/role.entity';
import { Permission } from '../../entities/permission.entity';
import { UserMenuAssignment } from '../../entities/user-menu-assignment.entity';
import { Menu } from '../../entities/menu.entity';
import { SubMenu } from '../../entities/submenu.entity';
// Affiliate entities removed

/**
 * Reset Menu Seeder
 * Clears existing menu data and recreates it
 */
async function resetMenuSeeder() {
  console.log('Starting menu reset and seeding...');

  // Create DataSource configuration
  const dataSource = new DataSource({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || 'Admin@123456',
    database: process.env.DB_DATABASE || 'cms_dev',
    entities: [
      User,
      Department,
      Team,
      Role,
      Permission,
      UserMenuAssignment,
      Menu,
      SubMenu,
    ],
    synchronize: true,
    logging: false,
  });

  try {
    // Initialize the data source
    await dataSource.initialize();
    console.log('Database connection established');

    const menuRepository = dataSource.getRepository(Menu);
    const submenuRepository = dataSource.getRepository(SubMenu);
    const userMenuAssignmentRepository = dataSource.getRepository(UserMenuAssignment);

    // Delete existing data in correct order (due to foreign key constraints)
    console.log('Clearing existing menu data...');
    
    // Delete user menu assignments first
    await userMenuAssignmentRepository.delete({});
    console.log('Deleted user menu assignments');

    // Delete submenus
    await submenuRepository.delete({});
    console.log('Deleted submenus');

    // Delete menus
    await menuRepository.delete({});
    console.log('Deleted menus');

    // Run menu seeder
    await MenuSeeder.run(dataSource);

    console.log('Menu reset and seeding completed successfully!');
  } catch (error) {
    console.error('Error running menu reset seeder:', error);
    process.exit(1);
  } finally {
    // Close the connection
    await dataSource.destroy();
    console.log('Database connection closed');
  }
}

// Run the reset menu seeder
resetMenuSeeder();
