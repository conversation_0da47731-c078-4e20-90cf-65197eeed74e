import { DataSource } from 'typeorm';
import { UserMenuAssignment } from '@/entities/user-menu-assignment.entity';
import { User } from '@/entities/user.entity';
import { Menu } from '@/entities/menu.entity';
import { SubMenu } from '@/entities/submenu.entity';

/**
 * UserMenuAssignment Seeder
 * Seeds sample user menu assignments
 */
export class UserMenuAssignmentSeeder {
  public static async run(dataSource: DataSource): Promise<void> {
    const userMenuAssignmentRepository = dataSource.getRepository(UserMenuAssignment);
    const userRepository = dataSource.getRepository(User);
    const menuRepository = dataSource.getRepository(Menu);
    const submenuRepository = dataSource.getRepository(SubMenu);

    // Check if assignments already exist
    const existingAssignments = await userMenuAssignmentRepository.count();
    if (existingAssignments > 0) {
      console.log('User menu assignments already exist, skipping seeder...');
      return;
    }

    console.log('Seeding user menu assignments...');

    // Get super admin user
    const superAdminUser = await userRepository.findOne({
      where: { username: 'super_admin' }
    });

    if (!superAdminUser) {
      console.log('Super admin user not found, skipping user menu assignment seeder...');
      return;
    }

    // Get all menus, submenus and permissions
    const menus = await menuRepository.find();
    const submenus = await submenuRepository.find({
      relations: ['menu']
    });
    const permissions = await dataSource.getRepository('Permission').find();

    // Assign all menus and submenus to super admin with full permissions
    const assignments: UserMenuAssignment[] = [];

    // Assign all main menus
    for (const menu of menus) {
      const assignment = userMenuAssignmentRepository.create({
        userId: superAdminUser.id,
        menuId: menu.id,
        permissions: permissions, // Assign all permissions
        createdBy: 'system',
      });
      assignments.push(assignment);
    }

    // Assign all submenus
    for (const submenu of submenus) {
      const assignment = userMenuAssignmentRepository.create({
        userId: superAdminUser.id,
        menuId: submenu.menu.id,
        submenuId: submenu.id,
        permissions: permissions, // Assign all permissions
        createdBy: 'system',
      });
      assignments.push(assignment);
    }

    // Save all assignments
    await userMenuAssignmentRepository.save(assignments);

    console.log(`Created ${assignments.length} user menu assignments for super admin`);
    console.log('User menu assignment seeding completed!');
  }
}
