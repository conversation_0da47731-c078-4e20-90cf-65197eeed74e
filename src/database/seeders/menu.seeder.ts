import { DataSource } from 'typeorm';
import { Menu } from '@/entities/menu.entity';
import { SubMenu } from '@/entities/submenu.entity';

/**
 * <PERSON><PERSON> Seeder
 * Seeds the menus and submenus table with predefined menu structure
 */
export class MenuSeeder {
  public static async run(dataSource: DataSource): Promise<void> {
    const menuRepository = dataSource.getRepository(Menu);
    const submenuRepository = dataSource.getRepository(SubMenu);

    // Check if menus already exist
    const existingMenus = await menuRepository.count();
    if (existingMenus > 0) {
      console.log('Menus already exist, skipping seeder...');
      return;
    }

    console.log('Seeding menus and submenus...');

    // Menu structure data
    const menuStructure = {
      "menus": [
        {
          "title": "Dashboard",
          "children": [
            {
              "title": "Biểu đồ thể hiện tăng trưởng của từng bộ phận theo mốc thời gian đư<PERSON><PERSON> chọn",
              "children": [
                {
                  "title": "Biểu đồ FTD"
                },
                {
                  "title": "Biểu đồ Tổng cược"
                },
                {
                  "title": "Biểu đồ số người nạp tiền"
                },
                {
                  "title": "Biểu đồ Doanh Thu"
                }
              ]
            },
            {
              "title": "Bảng báo cáo FTD theo bộ phận"
            },
            {
              "title": "Bảng báo cáo tổng theo từng hậu đài"
            }
          ]
        },
        {
          "title": "Doanh Thu",
          "children": [
            {
              "title": "SEO"
            },
            {
              "title": "Sale Khu A"
            },
            {
              "title": "Sale Khu B"
            },
            {
              "title": "ADS"
            },
            {
              "title": "Spam"
            },
            {
              "title": "New_fran"
            },
            {
              "title": "Bộ phận n"
            }
          ]
        },
        {
          "title": "Chi Phí",
          "children": [
            {
              "title": "Chi phí theo từng bộ phận",
              "description": "Link Google Drive"
            }
          ]
        },
        {
          "title": "Hiệu Suất Quảng Cáo",
          "children": [
            {
              "title": "Mỗi bộ phận có 1 cách bảng theo dõi hiệu suất khác nhau"
            }
          ]
        },
        {
          "title": "Quản Lý Dữ Liệu",
          "children": [
            {
              "title": "Up dữ liệu theo từng folder: đăng ký, nạp đầu, báo cáo thành viên..."
            }
          ]
        },
        {
          "title": "Quản Lý Bộ Phận",
          "children": [
            {
              "title": "Phân cấp theo bậc: Quản lý, tổ trưởng, tổ viên"
            },
            {
              "title": "Tạo hậu đài theo nhân viên"
            }
          ]
        }
      ]
    };

    // Process each menu
    for (const menuData of menuStructure.menus) {
      // Create main menu
      const menu = menuRepository.create({
        title: menuData.title,
        description: `Menu ${menuData.title}`,
        createdBy: 'system',
      });

      const savedMenu = await menuRepository.save(menu);
      console.log(`Created menu: ${savedMenu.title}`);

      // Process children (submenus)
      if (menuData.children && menuData.children.length > 0) {
        await this.createSubmenus(submenuRepository, menuData.children, savedMenu, null);
      }
    }

    console.log('Menu seeding completed!');
  }

  /**
   * Recursively create submenus
   */
  private static async createSubmenus(
    submenuRepository: any,
    children: any[],
    menu: Menu,
    parent: SubMenu | null
  ): Promise<void> {
    for (const childData of children) {
      // Create submenu
      const submenu = submenuRepository.create({
        title: childData.title,
        description: childData.description || null,
        link: this.generateLink(childData.title),
        menu: menu,
        parent: parent,
        createdBy: 'system',
      });

      const savedSubmenu = await submenuRepository.save(submenu);
      console.log(`Created submenu: ${savedSubmenu.title} (Parent: ${parent ? parent.title : menu.title})`);

      // Process nested children
      if (childData.children && childData.children.length > 0) {
        await this.createSubmenus(submenuRepository, childData.children, menu, savedSubmenu);
      }
    }
  }

  /**
   * Generate link from title
   */
  private static generateLink(title: string): string {
    return '/' + title
      .toLowerCase()
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
      .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
      .replace(/[ìíịỉĩ]/g, 'i')
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
      .replace(/[ùúụủũưừứựửữ]/g, 'u')
      .replace(/[ỳýỵỷỹ]/g, 'y')
      .replace(/đ/g, 'd')
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
}
