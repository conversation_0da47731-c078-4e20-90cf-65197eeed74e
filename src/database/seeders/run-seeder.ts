import { DataSource } from 'typeorm';
import { PermissionSeeder } from './permission.seeder';
import { RoleSeeder } from './role.seeder';
import { UserSeeder } from './user.seeder';
import { MenuSeeder } from './menu.seeder';
import { UserMenuAssignmentSeeder } from './user-menu-assignment.seeder';

// Import all entities
import { User } from '../../entities/user.entity';
import { Department } from '../../entities/department.entity';
import { Team } from '../../entities/team.entity';
import { Role } from '../../entities/role.entity';
import { Permission } from '../../entities/permission.entity';
import { UserMenuAssignment } from '../../entities/user-menu-assignment.entity';
import { UserRoleAssignment } from '../../entities/user-role-assignment.entity';
import { Menu } from '../../entities/menu.entity';
import { SubMenu } from '../../entities/submenu.entity';
import { Brand } from '../../entities/brand.entity';
import { Agency } from '../../entities/agency.entity';
import { Member } from '../../entities/member.entity';
import { MemberTransaction } from '../../entities/member-transaction.entity';
import { InvoiceProposals } from '../../entities/invoice-proposals.entity';
import { MemberStatistics } from '../../entities/member-statistics.entity';
import { FileEntity } from '../../entities/file.entity';
// Affiliate entities removed

/**
 * Main Seeder Runner
 * Runs all seeders in the correct order
 */
async function runSeeders() {
  console.log('Starting database seeding...');

  // Create DataSource configuration
  const dataSource = new DataSource({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'cms_db',
    entities: [
      User,
      Department,
      Team,
      Role,
      Permission,
      UserMenuAssignment,
      UserRoleAssignment,
      Menu,
      SubMenu,
      Brand,
      Agency,
      Member,
      MemberTransaction,
      InvoiceProposals,
      MemberStatistics,
      FileEntity,
    ],
    synchronize: true,
    logging: false,
  });

  try {
    // Initialize the data source
    await dataSource.initialize();
    console.log('Database connection established');

    // Run seeders in order
    await PermissionSeeder.run(dataSource);
    await RoleSeeder.run(dataSource);
    await UserSeeder.run(dataSource);
    await MenuSeeder.run(dataSource);
    await UserMenuAssignmentSeeder.run(dataSource);

    console.log('All seeders completed successfully!');
  } catch (error) {
    console.error('Error running seeders:', error);
    process.exit(1);
  } finally {
    // Close the connection
    await dataSource.destroy();
    console.log('Database connection closed');
  }
}

// Run the seeders
runSeeders();
