import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class UpdateTeamDepartmentRelation1704067200000 implements MigrationInterface {
  name = 'UpdateTeamDepartmentRelation1704067200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add description column to teams table
    await queryRunner.query(`
      ALTER TABLE teams 
      ADD COLUMN description TEXT NULL 
      COMMENT 'Description of the team'
    `);

    // Create junction table for team-department many-to-many relationship
    await queryRunner.createTable(
      new Table({
        name: 'team_departments',
        columns: [
          {
            name: 'team_id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
          },
          {
            name: 'department_id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
          },
        ],
        indices: [
          {
            name: 'IDX_team_departments_team_id',
            columnNames: ['team_id'],
          },
          {
            name: 'IDX_team_departments_department_id',
            columnNames: ['department_id'],
          },
        ],
      }),
      true,
    );

    // Add foreign key constraints
    await queryRunner.createForeignKey(
      'team_departments',
      new TableForeignKey({
        columnNames: ['team_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'teams',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'team_departments',
      new TableForeignKey({
        columnNames: ['department_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'departments',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    // Migrate existing data from teams.departmentId to team_departments junction table
    await queryRunner.query(`
      INSERT INTO team_departments (team_id, department_id)
      SELECT id, departmentId 
      FROM teams 
      WHERE departmentId IS NOT NULL
    `);

    // Remove the old departmentId column from teams table
    await queryRunner.query(`
      ALTER TABLE teams DROP COLUMN departmentId
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back departmentId column to teams table
    await queryRunner.query(`
      ALTER TABLE teams 
      ADD COLUMN departmentId VARCHAR(36) NULL 
      COMMENT 'Department ID that this team belongs to'
    `);

    // Migrate data back from junction table to teams.departmentId
    // Note: This will only work if each team belongs to exactly one department
    await queryRunner.query(`
      UPDATE teams t
      SET departmentId = (
        SELECT department_id 
        FROM team_departments td 
        WHERE td.team_id = t.id 
        LIMIT 1
      )
    `);

    // Drop the junction table
    await queryRunner.dropTable('team_departments');

    // Remove description column from teams table
    await queryRunner.query(`
      ALTER TABLE teams DROP COLUMN description
    `);

    // Add back foreign key constraint for teams.departmentId
    await queryRunner.createForeignKey(
      'teams',
      new TableForeignKey({
        columnNames: ['departmentId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'departments',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      }),
    );
  }
}
