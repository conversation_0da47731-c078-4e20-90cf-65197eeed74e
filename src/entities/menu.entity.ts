import { <PERSON>tity, Column, OneToMany } from 'typeorm';
import { SubMenu } from './submenu.entity';
import { UserMenuAssignment } from './user-menu-assignment.entity';
import { BaseEntity } from './base.entity';

@Entity()
export class Menu extends BaseEntity {

  @Column({ unique: true })
  title: string;

  @Column({ nullable: true })
  description: string;

  @OneToMany(() => SubMenu, submenu => submenu.menu, { cascade: true })
  submenus: SubMenu[];

  @OneToMany(() => UserMenuAssignment, assignment => assignment.menu)
  userAssignments: UserMenuAssignment[];
}
