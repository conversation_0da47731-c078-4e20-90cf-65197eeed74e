import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column,
} from 'typeorm';

/**
 * Base Entity với các trường chung
 */
export abstract class BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    type: 'timestamp',
    nullable: true,
  })
  deletedAt?: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'User who created this record',
  })
  createdBy?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'User who last updated this record',
  })
  updatedBy?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'User who deleted this record',
  })
  deletedBy?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional metadata for the record',
  })
  metadata?: Record<string, any>;
}
