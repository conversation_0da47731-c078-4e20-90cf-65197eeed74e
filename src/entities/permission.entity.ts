import { Entity, Column } from 'typeorm';
import { BaseEntity } from './base.entity';
import { PermissionsType } from '@/common/constants';

/**
 * Permission Entity
 * Represents permissions in the system
 */
@Entity('permissions')
export class Permission extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the permission',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Action name of the permission',
  })
  action: PermissionsType;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the permission',
  })
  description?: string;
}
