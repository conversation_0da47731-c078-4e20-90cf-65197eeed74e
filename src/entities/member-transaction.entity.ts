import { Entity, Column, Index, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Member } from './member.entity';

export enum TransactionType {
  DEPOSIT = 'DEPOSIT',
  WITHDRAW = 'WITHDRAW',
  BET = 'BET',
  PAYOUT = 'PAYOUT',
  BONUS = 'BONUS',
  DEDUCTION = 'DEDUCTION',
  ADJUSTMENT = 'ADJUSTMENT',
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

/**
 * MemberTransaction Entity
 * Represents member transactions in the system
 */
@Entity('member_transactions')
@Index('idx_member_transaction_member_id', ['member_id'])
@Index('idx_member_transaction_type', ['type'])
@Index('idx_member_transaction_status', ['status'])
@Index('idx_member_transaction_created_at', ['createdAt'])
export class MemberTransaction extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 36,
    comment: 'Member ID associated with the transaction',
  })
  member_id: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
    comment: 'Type of transaction',
  })
  type: TransactionType;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: 'Transaction amount',
  })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Transaction fee',
  })
  fee: number;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
    comment: 'Transaction status',
  })
  status: TransactionStatus;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Transaction description',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Reference number',
  })
  reference_number?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional transaction data',
  })
  transaction_data?: Record<string, any>;

  @Column({
    type: 'datetime',
    nullable: true,
    comment: 'Transaction processed date',
  })
  processed_at?: Date;

  // Relationships
  @ManyToOne(() => Member, (member) => member.transactions)
  @JoinColumn({ name: 'member_id', referencedColumnName: 'id' })
  member: Member;
}
