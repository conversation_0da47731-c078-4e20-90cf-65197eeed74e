import { Entity, Column, Index, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Agency } from './agency.entity';
import { MemberTransaction } from './member-transaction.entity';

/**
 * Member Entity
 * Represents members in the system
 */
@Entity('members')
@Index('idx_member_username', ['username'])
@Index('idx_member_agency_code', ['agency_code'])
export class Member extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Brand ID associated with the member',
  })
  brand_id: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Agency code for the member',
  })
  agency_code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Username of the member',
  })
  username: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Full name of the member',
  })
  fullname?: string;

  @Column({
    type: 'int',
    default: 1,
    comment: 'Level of the member',
  })
  level: number;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Member level description',
  })
  member_level?: string;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of deposit times',
  })
  deposit_times: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total deposit amount',
  })
  deposit_total: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total withdraw amount',
  })
  withdraw_total: number;

  @Column({
    type: 'datetime',
    nullable: true,
    comment: 'Last withdraw date',
  })
  withdraw_at?: Date;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total bet amount',
  })
  bet_total: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Valid bet amount',
  })
  valid_bet: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Payout amount',
  })
  payout: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Bonus amount',
  })
  bonus: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Deduction amount',
  })
  deduction: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Profit amount',
  })
  profit: number;

  @Column({
    type: 'datetime',
    nullable: true,
    comment: 'Last login date',
  })
  login_at?: Date;

  @Column({
    type: 'datetime',
    comment: 'Join date',
  })
  join_at: Date;

  // Relationships
  @ManyToOne(() => Agency, { nullable: true })
  @JoinColumn({ name: 'agency_code', referencedColumnName: 'code' })
  agency?: Agency;

  @OneToMany(() => MemberTransaction, (transaction) => transaction.member)
  transactions: MemberTransaction[];
}