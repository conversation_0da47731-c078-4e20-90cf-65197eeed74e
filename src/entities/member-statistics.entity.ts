import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from './base.entity';

@Entity('member_statistics')
@Index('idx_member_statistics_username', ['username']) // Optional index if needed on the username column
export class MemberStatistics  extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'User ID',
    nullable: true,
  })
  brand_id: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  agency_code: string;

  @Column({ type: 'varchar', length: 255 , nullable: true })
  username: string;

  @Column({ type: 'int' , nullable: true })
  deposit_times: number;

  @Column({ type: 'float', nullable: true })
  deposit_total: number;

  @Column({ type: 'float' , nullable: true })
  withdraw_total: number;

  @Column({ type: 'datetime', nullable: true })
  withdraw_at: Date;

  @Column({ type: 'float', nullable: true })
  bet: number;

  @Column({ type: 'float', nullable: true })
  valid_bet: number;

  @Column({ type: 'float' , nullable: true })
  payout: number;

  @Column({ type: 'float', nullable: true })
  bonus: number;

  @Column({ type: 'float', nullable: true })
  deduction: number;

  @Column({ type: 'float' , nullable: true })
  profit: number;

  @Column({ type: 'datetime', nullable: true })
  login_at: Date;

  @Column({ type: 'datetime', nullable: true })
  join_at: Date;
}
