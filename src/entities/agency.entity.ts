import { Entity, Column, Index, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Member } from './member.entity';

/**
 * Agency Entity
 * Represents agencies in the system
 */
@Entity('agencies')
@Index('idx_agency_code', ['code'], { unique: true })
export class Agency extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Brand ID associated with the agency',
  })
  brand_id: string;

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Unique agency code',
  })
  code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Agency name',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Agency email',
  })
  email?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Agency phone',
  })
  phone?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Agency address',
  })
  address?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Extra information about the agency',
  })
  extra?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of the agency',
  })
  description?: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the agency is active',
  })
  active: boolean;

  // Relationships
  @OneToMany(() => Member, (member) => member.agency)
  members: Member[];
}
