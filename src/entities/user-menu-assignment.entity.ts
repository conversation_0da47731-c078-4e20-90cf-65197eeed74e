import { <PERSON>tity, Column, ManyToOne, ManyToMany, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, JoinTable, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';
import { Menu } from './menu.entity';
import { SubMenu } from './submenu.entity';
import { Permission } from './permission.entity';

/**
 * UserMenuAssignment Entity
 * Junction table between User and Menu/SubMenu for menu access permissions
 */
@Entity('user_menu_assignments')
@Index(['userId', 'menuId'], { unique: false })
@Index(['userId', 'submenuId'], { unique: false })
export class UserMenuAssignment extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'User ID',
  })
  userId: string;

  @Column({
    type: 'uuid',
    comment: 'Menu ID',
  })
  menuId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'SubMenu ID (optional for submenu-specific permissions)',
  })
  submenuId?: string;

  // Many-to-many relationship with permissions
  @ManyToMany(() => Permission)
  @JoinTable({
    name: 'user_menu_assignment_permissions',
    joinColumn: {
      name: 'userMenuAssignmentId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'permissionId',
      referencedColumnName: 'id',
    },
  })
  permissions: Permission[];

  // Relationships
  @ManyToOne(() => User, (user) => user.menuAssignments)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Menu, (menu) => menu.userAssignments)
  @JoinColumn({ name: 'menuId' })
  menu: Menu;

  @ManyToOne(() => SubMenu, (submenu) => submenu.userAssignments, { nullable: true })
  @JoinColumn({ name: 'submenuId' })
  submenu?: SubMenu;
}
