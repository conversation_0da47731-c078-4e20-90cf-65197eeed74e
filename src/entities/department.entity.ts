import { En<PERSON>ty, Column, ManyToMany, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Team } from './team.entity';
import { User } from './user.entity';
import { Agency } from './agency.entity';
import { Brand } from './brand.entity';

/**
 * Department Entity
 * Represents departments in the organization
 */
@Entity('departments')
export class Department extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the department',
  })
  name: string;

  @ManyToOne(() => Agency, { nullable: true })
  @JoinColumn({ name: 'agencyId' })
  agency?: Agency;

  @ManyToOne(() => Brand, (brand) => brand.departments, { nullable: true })
  brand: Brand[];

  // Relationships
  @ManyToMany(() => Team, (team) => team.departments)
  teams: Team[];

  @ManyToMany(() => User, (user) => user.departments)
  users: User[];
}
