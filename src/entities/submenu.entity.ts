import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON>ne, OneToMany } from 'typeorm';
import { Menu } from './menu.entity';
import { UserMenuAssignment } from './user-menu-assignment.entity';
import { BaseEntity } from './base.entity';

@Entity()
export class SubMenu extends BaseEntity {
  @Column()
  title: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  link: string;

  @ManyToOne(() => Menu, menu => menu.submenus, { nullable: true, onDelete: 'CASCADE' })
  menu: Menu;

  @ManyToOne(() => SubMenu, submenu => submenu.children, { nullable: true, onDelete: 'CASCADE' })
  parent: SubMenu;

  @OneToMany(() => SubMenu, submenu => submenu.parent, { cascade: true })
  children: SubMenu[];

  @OneToMany(() => UserMenuAssignment, assignment => assignment.submenu)
  userAssignments: UserMenuAssignment[];
}
