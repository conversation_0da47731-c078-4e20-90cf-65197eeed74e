import { <PERSON><PERSON>ty, Column, ManyToOne, ManyToMany, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, JoinTable, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';
import { Role } from './role.entity';

/**
 * UserMenuAssignment Entity
 * Junction table between User and Menu/SubMenu for menu access permissions
 */
@Entity('user_role_assignments')
@Index(['userId', 'roleId'], { unique: false })
export class UserRoleAssignment extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'User ID',
  })
  userId: string;

  @Column({
    type: 'uuid',
    comment: 'Role ID',
  })
  roleId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Department ID (optional for department-specific permissions)',
  })
  departmentId?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Team ID (optional for team-specific permissions)',
  })
  teamId?: string;

  // Relationships
  @ManyToOne(() => User, (user) => user.roleAssignments)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Role, (role) => role.userRoleAssignments)
  @JoinColumn({ name: 'roleId' })
  role: Role;
}
