import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class AssignableRoleDto {
  @Expose()
  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @Expose()
  @ApiProperty({
    description: 'Role name',
    example: 'manager_department',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Role display name',
    example: 'Manager Bộ phận',
  })
  displayName: string;

  @Expose()
  @ApiProperty({
    description: 'Role level (1 = highest, 4 = lowest)',
    example: 2,
  })
  level: number;

  @Expose()
  @ApiProperty({
    description: 'Role description',
    example: 'Quản lý 1 bộ phận',
  })
  description?: string;

  @Expose()
  @ApiProperty({
    description: 'Whether this is a system role',
    example: false,
  })
  isSystem: boolean;
}

export class AssignableRolesResponseDto {
  @Expose()
  @Type(() => AssignableRoleDto)
  @ApiProperty({
    description: 'List of roles that current user can assign',
    type: [AssignableRoleDto],
  })
  roles: AssignableRoleDto[];

  @Expose()
  @ApiProperty({
    description: 'Current user role information',
    nullable: true,
  })
  currentUserRole: {
    name: string;
    level: number;
    displayName: string;
  } | null;
}
