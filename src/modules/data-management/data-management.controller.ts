import {
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles, Permissions } from '@/common/decorators';
import { PermissionsType, RoleType } from '@/common/constants';
import { AllQueuesStatsDto, JobProgressDto, QueueStatsDto } from './dto/import-export.dto';
import { QueueService } from '@/common/queue/queue.service';

@ApiTags('Data Management')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@Controller('data-management')
export class DataManagementController {
  constructor(
    private readonly queueService: QueueService,
  ) { }
  
  @Get('jobs/:queueName/:jobId')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy thông tin job',
    description: 'Lấy thông tin chi tiết và trạng thái của một job cụ thể'
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiParam({ name: 'jobId', description: 'ID của job', example: '12345' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin job',
    type: JobProgressDto
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Job không tồn tại'
  })
  async getJobStatus(
    @Param('queueName') queueName: string,
    @Param('jobId') jobId: string,
  ): Promise<JobProgressDto> {
    const job = await this.queueService.getJob(queueName, jobId);

    if (!job) {
      throw new Error('Job not found');
    }

    return {
      id: job.id,
      name: job.name,
      status: await job.getState(),
      progress: job.progress(),
      data: job.data,
      attemptsMade: job.attemptsMade,
      timestamp: job.timestamp,
      failedReason: job.failedReason,
      result: job.returnvalue,
      finishedOn: job.finishedOn,
    };
  }

  @Get('queues/stats')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy thống kê tất cả queues',
    description: 'Lấy thống kê tổng quan về tất cả các queues trong hệ thống'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thống kê tất cả queues',
    type: AllQueuesStatsDto
  })
  async getAllQueuesStats(): Promise<AllQueuesStatsDto> {
    return await this.queueService.getAllQueuesStats();
  }

  @Get('queues/:queueName/stats')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy thống kê queue cụ thể',
    description: 'Lấy thống kê chi tiết của một queue cụ thể'
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thống kê queue',
    type: QueueStatsDto
  })
  async getQueueStats(@Param('queueName') queueName: string): Promise<QueueStatsDto> {
    return await this.queueService.getQueueStats(queueName);
  }

  @Delete('jobs/:queueName/:jobId')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  @ApiOperation({
    summary: 'Xóa job',
    description: 'Xóa một job khỏi queue'
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiParam({ name: 'jobId', description: 'ID của job', example: '12345' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job đã được xóa thành công'
  })
  async removeJob(
    @Param('queueName') queueName: string,
    @Param('jobId') jobId: string,
  ): Promise<{ message: string }> {
    await this.queueService.removeJob(queueName, jobId);
    return { message: 'Job removed successfully' };
  }

  @Post('queues/:queueName/clean')
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.DELETE)
  @ApiOperation({
    summary: 'Dọn dẹp queue',
    description: 'Xóa các job completed/failed cũ khỏi queue'
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiQuery({ name: 'grace', required: false, description: 'Grace period in milliseconds', example: 3600000 })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum number of jobs to clean', example: 100 })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue đã được dọn dẹp thành công'
  })
  async cleanQueue(
    @Param('queueName') queueName: string,
    @Query('grace') grace: number = 3600000, // 1 hour default
    @Query('limit') limit: number = 100,
  ): Promise<{ message: string }> {
    await this.queueService.cleanQueue(queueName, grace, limit);
    return { message: `Queue ${queueName} cleaned successfully` };
  }

  @Post('queues/:queueName/pause')
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({
    summary: 'Tạm dừng queue',
    description: 'Tạm dừng xử lý jobs trong queue'
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue đã được tạm dừng'
  })
  async pauseQueue(@Param('queueName') queueName: string): Promise<{ message: string }> {
    await this.queueService.pauseQueue(queueName);
    return { message: `Queue ${queueName} paused successfully` };
  }

  @Post('queues/:queueName/resume')
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({
    summary: 'Tiếp tục queue',
    description: 'Tiếp tục xử lý jobs trong queue đã bị tạm dừng'
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue đã được tiếp tục'
  })
  async resumeQueue(@Param('queueName') queueName: string): Promise<{ message: string }> {
    await this.queueService.resumeQueue(queueName);
    return { message: `Queue ${queueName} resumed successfully` };
  }

  /**
   * Get estimated processing time based on entity type and operation
   */
  private getEstimatedProcessingTime(entityType: string, operation: 'import' | 'export'): number {
    const baseTime = operation === 'import' ? 5 : 3; // minutes

    const multipliers: { [key: string]: number } = {
      users: 1.0,
      affiliates: 0.5,
      bets: 1.5,
      deposits: 1.2,
      costs: 0.8,
      ad_performance: 1.0,
    };

    return Math.ceil(baseTime * (multipliers[entityType] || 1.0));
  }
}