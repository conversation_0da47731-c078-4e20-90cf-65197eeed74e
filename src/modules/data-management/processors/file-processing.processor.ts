import { Processor, Process } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { Job } from 'bull';
import * as fs from 'fs';
import * as path from 'path';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { QUEUE_NAMES, JOB_TYPES } from '@/common/queue/queue.constants';

export interface FileProcessingJobData {
  filePath: string;
  userId: number;
  action: 'validate' | 'convert' | 'compress' | 'analyze';
  options?: {
    outputPath?: string;
    format?: string;
    quality?: number;
    [key: string]: any;
  };
}

export interface CleanupJobData {
  directory: string;
  maxAge: number; // in hours
  pattern?: string;
  dryRun?: boolean;
}

@Injectable()
@Processor(QUEUE_NAMES.FILE_PROCESSING)
export class FileProcessingProcessor {
  constructor(
    private readonly logger: CustomLoggerService,
  ) {}

  @Process(JOB_TYPES.PROCESS_UPLOADED_FILE)
  async processUploadedFile(job: Job<FileProcessingJobData>) {
    const { filePath, userId, action, options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting file processing: ${action} for ${filePath}`, 'FileProcessingProcessor');

      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const fileStats = fs.statSync(filePath);
      let result: any = {
        originalPath: filePath,
        originalSize: fileStats.size,
        action,
        userId,
      };

      await job.progress(25);

      switch (action) {
        case 'validate':
          result = await this.validateFile(filePath, options);
          break;
        case 'convert':
          result = await this.convertFile(filePath, options);
          break;
        case 'compress':
          result = await this.compressFile(filePath, options);
          break;
        case 'analyze':
          result = await this.analyzeFile(filePath, options);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      await job.progress(100);

      this.logger.info(
        `File processing completed: ${action} for ${filePath}`,
        'FileProcessingProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`File processing failed: ${error.message}`, error.stack, 'FileProcessingProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.CLEANUP_TEMP_FILES)
  async cleanupTempFiles(job: Job<CleanupJobData>) {
    const { directory, maxAge, pattern = '*', dryRun = false } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting cleanup of ${directory} (max age: ${maxAge}h)`, 'FileProcessingProcessor');

      if (!fs.existsSync(directory)) {
        throw new Error(`Directory not found: ${directory}`);
      }

      const cutoffTime = Date.now() - (maxAge * 60 * 60 * 1000); // Convert hours to milliseconds
      const files = fs.readdirSync(directory);
      let deletedCount = 0;
      let totalSize = 0;
      const deletedFiles: string[] = [];

      await job.progress(25);

      for (const file of files) {
        const filePath = path.join(directory, file);
        
        try {
          const stats = fs.statSync(filePath);
          
          // Skip directories
          if (stats.isDirectory()) {
            continue;
          }

          // Check if file is older than cutoff time
          if (stats.mtime.getTime() < cutoffTime) {
            totalSize += stats.size;
            deletedFiles.push(file);

            if (!dryRun) {
              fs.unlinkSync(filePath);
            }
            deletedCount++;
          }
        } catch (error) {
          this.logger.warn(`Failed to process file ${filePath}: ${error.message}`, 'FileProcessingProcessor');
        }
      }

      await job.progress(100);

      const result = {
        directory,
        maxAge,
        deletedCount,
        totalSize,
        dryRun,
        deletedFiles: deletedFiles.slice(0, 100), // Limit to prevent memory issues
      };

      this.logger.info(
        `Cleanup completed: ${deletedCount} files ${dryRun ? 'would be' : ''} deleted (${this.formatBytes(totalSize)})`,
        'FileProcessingProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Cleanup failed: ${error.message}`, error.stack, 'FileProcessingProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.GENERATE_REPORT)
  async generateReport(job: Job<any>) {
    const { reportType, filters, userId, outputPath } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting report generation: ${reportType}`, 'FileProcessingProcessor');

      // This is a placeholder for report generation logic
      // In a real implementation, you would:
      // 1. Query the database based on reportType and filters
      // 2. Process the data
      // 3. Generate the report in the desired format
      // 4. Save to outputPath

      await job.progress(50);

      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${reportType}_report_${timestamp}.pdf`;
      const filePath = outputPath || path.join('./uploads/reports', fileName);

      // Ensure reports directory exists
      const reportsDir = path.dirname(filePath);
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }

      // Create a dummy report file (in real implementation, generate actual report)
      const reportContent = `Report: ${reportType}\nGenerated: ${new Date().toISOString()}\nFilters: ${JSON.stringify(filters, null, 2)}`;
      fs.writeFileSync(filePath, reportContent);

      await job.progress(100);

      const result = {
        reportType,
        fileName,
        filePath,
        userId,
        generatedAt: new Date(),
      };

      this.logger.info(
        `Report generation completed: ${fileName}`,
        'FileProcessingProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Report generation failed: ${error.message}`, error.stack, 'FileProcessingProcessor');
      throw error;
    }
  }

  /**
   * Validate file format and content
   */
  private async validateFile(filePath: string, options: any): Promise<any> {
    const stats = fs.statSync(filePath);
    const ext = path.extname(filePath).toLowerCase();
    
    const result = {
      isValid: true,
      fileSize: stats.size,
      fileExtension: ext,
      mimeType: this.getMimeType(ext),
      errors: [] as string[],
      warnings: [] as string[],
    };

    // Basic validation rules
    if (stats.size === 0) {
      result.isValid = false;
      result.errors.push('File is empty');
    }

    if (stats.size > 100 * 1024 * 1024) { // 100MB
      result.warnings.push('File is larger than 100MB');
    }

    // Add more validation logic based on file type
    if (['.csv', '.txt'].includes(ext)) {
      // Validate text files
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        if (content.length === 0) {
          result.isValid = false;
          result.errors.push('File content is empty');
        }
      } catch (error) {
        result.isValid = false;
        result.errors.push('Cannot read file content');
      }
    }

    return result;
  }

  /**
   * Convert file to different format
   */
  private async convertFile(filePath: string, options: any): Promise<any> {
    const { outputPath, format } = options;
    
    // This is a placeholder for file conversion logic
    // In a real implementation, you would use appropriate libraries
    // like sharp for images, ffmpeg for videos, etc.
    
    const result = {
      originalPath: filePath,
      convertedPath: outputPath,
      originalFormat: path.extname(filePath),
      targetFormat: format,
      success: true,
    };

    // Simulate conversion by copying file
    if (outputPath) {
      fs.copyFileSync(filePath, outputPath);
    }

    return result;
  }

  /**
   * Compress file
   */
  private async compressFile(filePath: string, options: any): Promise<any> {
    const { outputPath, quality = 80 } = options;
    
    // This is a placeholder for file compression logic
    // In a real implementation, you would use appropriate compression libraries
    
    const originalStats = fs.statSync(filePath);
    const result = {
      originalPath: filePath,
      compressedPath: outputPath,
      originalSize: originalStats.size,
      compressedSize: Math.floor(originalStats.size * 0.7), // Simulate 30% compression
      compressionRatio: 0.3,
      quality,
    };

    // Simulate compression by copying file
    if (outputPath) {
      fs.copyFileSync(filePath, outputPath);
    }

    return result;
  }

  /**
   * Analyze file content and metadata
   */
  private async analyzeFile(filePath: string, options: any): Promise<any> {
    const stats = fs.statSync(filePath);
    const ext = path.extname(filePath).toLowerCase();
    
    const result = {
      fileName: path.basename(filePath),
      fileSize: stats.size,
      fileExtension: ext,
      mimeType: this.getMimeType(ext),
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime,
      analysis: {} as any,
    };

    // Add specific analysis based on file type
    if (['.csv', '.txt'].includes(ext)) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        result.analysis = {
          lineCount: content.split('\n').length,
          characterCount: content.length,
          encoding: 'utf8',
        };
      } catch (error) {
        result.analysis.error = 'Cannot analyze file content';
      }
    }

    return result;
  }

  /**
   * Get MIME type based on file extension
   */
  private getMimeType(extension: string): string {
    const mimeTypes: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.pdf': 'application/pdf',
      '.csv': 'text/csv',
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
