import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
// User analytics entity removed - using string types
export enum RegistrationSource {
  WEBSITE = 'WEBSITE',
  MOBILE = 'MOBILE',
  REFERRAL = 'REFERRAL',
}

export enum MemberStatus {
  NEW = 'NEW',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

export enum ActivityLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
}

export enum GeographicRegion {
  NORTH = 'NORTH',
  SOUTH = 'SOUTH',
  CENTRAL = 'CENTRAL',
}

export class UserAnalyticsDepartmentDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;
}

export class UserAnalyticsUserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  full_name: string;
}

export class UserAnalyticsResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty({ description: 'ID người dùng' })
  user_id: string;

  @ApiProperty({ type: UserAnalyticsUserDto, description: 'Thông tin người dùng' })
  @Type(() => UserAnalyticsUserDto)
  user: UserAnalyticsUserDto;

  // Registration Information
  @ApiProperty({ description: 'Ngày đăng ký' })
  registration_date: Date;

  @ApiProperty({ enum: RegistrationSource, description: 'Nguồn đăng ký' })
  registration_source: RegistrationSource;

  @ApiPropertyOptional({ description: 'Tên chiến dịch đăng ký' })
  registration_campaign?: string;

  @ApiPropertyOptional({ description: 'Mã giới thiệu' })
  referral_code?: string;

  @ApiPropertyOptional({ description: 'ID người giới thiệu' })
  referred_by_user_id?: string;

  @ApiPropertyOptional({ type: UserAnalyticsUserDto, description: 'Người giới thiệu' })
  @Type(() => UserAnalyticsUserDto)
  referred_by_user?: UserAnalyticsUserDto;

  @ApiPropertyOptional({ description: 'IP đăng ký' })
  registration_ip?: string;

  @ApiPropertyOptional({ description: 'Thiết bị đăng ký' })
  registration_device?: string;

  @ApiPropertyOptional({ description: 'Trình duyệt đăng ký' })
  registration_browser?: string;

  // Member Status & Classification
  @ApiProperty({ enum: MemberStatus, description: 'Trạng thái thành viên' })
  member_status: MemberStatus;

  @ApiPropertyOptional({ description: 'Ngày cập nhật trạng thái' })
  status_updated_date?: Date;

  @ApiProperty({ enum: ActivityLevel, description: 'Mức độ hoạt động' })
  activity_level: ActivityLevel;

  @ApiProperty({ description: 'Hạng thành viên' })
  member_tier: number;

  @ApiProperty({ description: 'Giá trị lifetime' })
  lifetime_value: number;

  @ApiProperty({ description: 'Giá trị dự đoán' })
  predicted_value: number;

  @ApiProperty({ description: 'Xác suất churn (%)' })
  churn_probability: number;

  // Activity Metrics
  @ApiProperty({ description: 'Tổng số lần đăng nhập' })
  total_logins: number;

  @ApiPropertyOptional({ description: 'Ngày đăng nhập cuối' })
  last_login_date?: Date;

  @ApiProperty({ description: 'Số ngày kể từ lần đăng nhập cuối' })
  days_since_last_login: number;

  @ApiProperty({ description: 'Tổng số phiên làm việc' })
  total_sessions: number;

  @ApiProperty({ description: 'Thời gian phiên trung bình (giây)' })
  avg_session_duration: number;

  @ApiProperty({ description: 'Số lượt xem trang' })
  page_views: number;

  // Financial Metrics
  @ApiProperty({ description: 'Tổng tiền gửi' })
  total_deposits: number;

  @ApiProperty({ description: 'Số lần gửi tiền' })
  deposit_count: number;

  @ApiProperty({ description: 'Số tiền gửi lần đầu' })
  first_deposit_amount: number;

  @ApiPropertyOptional({ description: 'Ngày gửi tiền lần đầu' })
  first_deposit_date?: Date;

  @ApiPropertyOptional({ description: 'Ngày gửi tiền cuối' })
  last_deposit_date?: Date;

  @ApiProperty({ description: 'Số ngày để gửi tiền lần đầu' })
  days_to_first_deposit: number;

  @ApiProperty({ description: 'Số ngày kể từ lần gửi tiền cuối' })
  days_since_last_deposit: number;

  @ApiProperty({ description: 'Số tiền gửi trung bình' })
  avg_deposit_amount: number;

  @ApiProperty({ description: 'Tổng tiền rút' })
  total_withdrawals: number;

  @ApiProperty({ description: 'Số lần rút tiền' })
  withdrawal_count: number;

  @ApiProperty({ description: 'Số dư hiện tại' })
  current_balance: number;

  @ApiProperty({ description: 'Tổng tiền thắng' })
  total_winnings: number;

  @ApiProperty({ description: 'Tổng tiền thua' })
  total_losses: number;

  @ApiProperty({ description: 'Tỷ lệ thắng/thua' })
  win_loss_ratio: number;

  // Betting Metrics
  @ApiProperty({ description: 'Tổng số cược' })
  total_bets: number;

  @ApiProperty({ description: 'Tổng số tiền cược' })
  total_bet_amount: number;

  @ApiProperty({ description: 'Số tiền cược trung bình' })
  avg_bet_amount: number;

  @ApiProperty({ description: 'Số cược thắng' })
  winning_bets: number;

  @ApiProperty({ description: 'Số cược thua' })
  losing_bets: number;

  @ApiProperty({ description: 'Tỷ lệ thắng (%)' })
  win_rate: number;

  @ApiPropertyOptional({ description: 'Ngày cược cuối' })
  last_bet_date?: Date;

  @ApiProperty({ description: 'Số ngày kể từ lần cược cuối' })
  days_since_last_bet: number;

  // Geographic & Demographic Data
  @ApiPropertyOptional({ description: 'Quốc gia' })
  country?: string;

  @ApiPropertyOptional({ description: 'Thành phố' })
  city?: string;

  @ApiPropertyOptional({ enum: GeographicRegion, description: 'Khu vực địa lý' })
  region?: GeographicRegion;

  @ApiPropertyOptional({ description: 'Nhóm tuổi' })
  age_group?: string;

  @ApiPropertyOptional({ description: 'Giới tính' })
  gender?: string;

  @ApiPropertyOptional({ description: 'Ngôn ngữ ưa thích' })
  preferred_language?: string;

  @ApiPropertyOptional({ description: 'Múi giờ' })
  timezone?: string;

  // Communication Preferences
  @ApiProperty({ description: 'Thông báo email' })
  email_notifications: boolean;

  @ApiProperty({ description: 'Thông báo SMS' })
  sms_notifications: boolean;

  @ApiProperty({ description: 'Thông báo push' })
  push_notifications: boolean;

  @ApiProperty({ description: 'Email marketing' })
  marketing_emails: boolean;

  @ApiProperty({ description: 'Số lần mở email' })
  email_opens: number;

  @ApiProperty({ description: 'Số lần click email' })
  email_clicks: number;

  @ApiProperty({ description: 'Tỷ lệ mở email (%)' })
  email_open_rate: number;

  @ApiProperty({ description: 'Tỷ lệ click email (%)' })
  email_click_rate: number;

  // Risk & Compliance
  @ApiProperty({ description: 'Điểm rủi ro' })
  risk_score: number;

  @ApiProperty({ description: 'KYC đã xác minh' })
  kyc_verified: boolean;

  @ApiPropertyOptional({ description: 'Ngày xác minh KYC' })
  kyc_verified_date?: Date;

  @ApiProperty({ description: 'Hoạt động đáng nghi' })
  suspicious_activity: boolean;

  @ApiProperty({ description: 'Số cờ tuân thủ' })
  compliance_flags: number;

  @ApiPropertyOptional({ description: 'Ghi chú' })
  notes?: string;

  // Segmentation Tags
  @ApiPropertyOptional({ description: 'Tags (JSON)' })
  tags?: string;

  @ApiPropertyOptional({ description: 'Phân khúc khách hàng' })
  customer_segment?: string;

  @ApiPropertyOptional({ description: 'Persona marketing' })
  marketing_persona?: string;

  // Attribution & Campaign Data
  @ApiPropertyOptional({ description: 'UTM Source' })
  utm_source?: string;

  @ApiPropertyOptional({ description: 'UTM Medium' })
  utm_medium?: string;

  @ApiPropertyOptional({ description: 'UTM Campaign' })
  utm_campaign?: string;

  @ApiPropertyOptional({ description: 'UTM Term' })
  utm_term?: string;

  @ApiPropertyOptional({ description: 'UTM Content' })
  utm_content?: string;

  @ApiProperty({ description: 'Chi phí thu hút khách hàng' })
  acquisition_cost: number;

  // Department Assignment
  @ApiPropertyOptional({ type: UserAnalyticsDepartmentDto, description: 'Thông tin phòng ban' })
  @Type(() => UserAnalyticsDepartmentDto)
  department?: UserAnalyticsDepartmentDto;

  // Timestamps
  @ApiProperty({ description: 'Ngày tạo' })
  created_at: Date;

  @ApiProperty({ description: 'Ngày cập nhật' })
  updated_at: Date;

  @ApiPropertyOptional({ description: 'Ngày tính toán cuối' })
  last_calculated?: Date;

  // Computed Properties
  @ApiProperty({ description: 'Là người dùng mới' })
  isNewUser: boolean;

  @ApiProperty({ description: 'Là người dùng hoạt động' })
  isActiveUser: boolean;

  @ApiProperty({ description: 'Là khách hàng có giá trị cao' })
  isHighValue: boolean;

  @ApiProperty({ description: 'Đang có nguy cơ rời bỏ' })
  isAtRisk: boolean;

  @ApiProperty({ description: 'Đã từng gửi tiền' })
  hasDeposited: boolean;

  @ApiProperty({ description: 'Số ngày kể từ đăng ký' })
  daysSinceRegistration: number;

  @ApiPropertyOptional({ description: 'Số ngày từ đăng ký đến gửi tiền lần đầu' })
  registrationToFirstDepositDays?: number | null;

  @ApiProperty({ description: 'Thời gian phiên trung bình (phút)' })
  avgSessionDurationMinutes: number;

  @ApiProperty({ description: 'Giá trị lifetime đã format' })
  formattedLifetimeValue: string;

  @ApiProperty({ description: 'Tổng tiền gửi đã format' })
  formattedTotalDeposits: string;

  @ApiProperty({ description: 'Lãi/lỗ' })
  profitLoss: number;

  @ApiProperty({ description: 'Lãi/lỗ đã format' })
  formattedProfitLoss: string;

  @ApiProperty({ description: 'Điểm tương tác' })
  engagementScore: number;

  @ApiProperty({ description: 'Giai đoạn lifecycle khách hàng' })
  customerLifetimeStage: string;
} 