import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MemberTransaction } from '@/entities/member-transaction.entity';
import { Member } from '@/entities/member.entity';
import { MemberTransactionsService } from './member-transactions.service';
import { MemberTransactionsController } from './member-transactions.controller';
import { CommonServicesModule } from '@/common/services/common-services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([MemberTransaction, Member]),
    CommonServicesModule,
  ],
  controllers: [MemberTransactionsController],
  providers: [MemberTransactionsService],
  exports: [MemberTransactionsService],
})
export class MemberTransactionsModule {}
