import { IsArray, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateMemberTransactionDto } from './create-member-transaction.dto';

export class BulkCreateMemberTransactionDto {
  @ApiProperty({
    description: 'Array of member transactions to create',
    type: [CreateMemberTransactionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateMemberTransactionDto)
  transactions: CreateMemberTransactionDto[];
}

export class BulkCreateMemberTransactionResponseDto {
  @ApiProperty({
    description: 'Number of successfully created transactions',
    example: 95,
  })
  success: number;

  @ApiProperty({
    description: 'Number of failed transaction creations',
    example: 5,
  })
  failed: number;

  @ApiProperty({
    description: 'Total number of transactions processed',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Array of error messages for failed creations',
    type: [String],
    example: ['Member ID 123 not found', 'Invalid amount for transaction'],
  })
  errors: string[];

  @ApiProperty({
    description: 'Array of successfully created transaction IDs',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-426614174000', '456e7890-e89b-12d3-a456-426614174001'],
  })
  createdIds: string[];
}
