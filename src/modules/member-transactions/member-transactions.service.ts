import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { MemberTransaction, TransactionStatus } from '@/entities/member-transaction.entity';
import { Member } from '@/entities/member.entity';
import { CreateMemberTransactionDto } from './dto/create-member-transaction.dto';
import { UpdateMemberTransactionDto } from './dto/update-member-transaction.dto';
import { BulkCreateMemberTransactionDto, BulkCreateMemberTransactionResponseDto } from './dto/bulk-create-member-transaction.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class MemberTransactionsService {
  constructor(
    @InjectRepository(MemberTransaction)
    private readonly transactionRepository: Repository<MemberTransaction>,
    @InjectRepository(Member)
    private readonly memberRepository: Repository<Member>,
    private readonly dataSource: DataSource,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createTransactionDto: CreateMemberTransactionDto, createdBy?: string): Promise<MemberTransaction> {
    this.logger.info(
      `Creating new transaction for member: ${createTransactionDto.member_id}`,
      'MemberTransactionsService',
      { member_id: createTransactionDto.member_id, type: createTransactionDto.type, amount: createTransactionDto.amount, createdBy }
    );

    try {
      // Verify member exists
      const member = await this.memberRepository.findOne({
        where: { id: createTransactionDto.member_id },
      });
      if (!member) {
        this.logger.warn(
          `Transaction creation failed - member not found: ${createTransactionDto.member_id}`,
          'MemberTransactionsService'
        );
        throw new NotFoundException('Member not found');
      }

      const transaction = this.transactionRepository.create({
        ...createTransactionDto,
        fee: createTransactionDto.fee || 0,
        status: createTransactionDto.status || TransactionStatus.PENDING,
        createdBy,
      });

      const savedTransaction = await this.transactionRepository.save(transaction);

      this.logger.info(
        `Transaction created successfully: ${savedTransaction.id}`,
        'MemberTransactionsService',
        { transactionId: savedTransaction.id, member_id: createTransactionDto.member_id }
      );

      return savedTransaction;
    } catch (error) {
      this.logger.error(
        `Failed to create transaction for member: ${createTransactionDto.member_id}`,
        error,
        'MemberTransactionsService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<MemberTransaction>> {
    this.logger.info('Fetching all transactions', 'MemberTransactionsService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
        .leftJoinAndSelect('transaction.member', 'member');

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`transaction.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('transaction.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [transactions, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${transactions.length} transactions`,
        'MemberTransactionsService',
        { total, page, limit }
      );

      return createPaginatedResult(transactions, total, page, limit);
    } catch (error) {
      this.logger.error('Failed to fetch transactions', error, 'MemberTransactionsService');
      throw error;
    }
  }

  async findOne(id: string): Promise<MemberTransaction> {
    this.logger.info(`Fetching transaction by ID: ${id}`, 'MemberTransactionsService');

    try {
      const transaction = await this.transactionRepository.findOne({
        where: { id },
        relations: ['member'],
      });

      if (!transaction) {
        this.logger.warn(`Transaction not found: ${id}`, 'MemberTransactionsService');
        throw new NotFoundException('Transaction not found');
      }

      this.logger.info(`Transaction found: ${transaction.id}`, 'MemberTransactionsService', { transactionId: id });
      return transaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch transaction: ${id}`, error, 'MemberTransactionsService');
      throw error;
    }
  }

  async update(id: string, updateTransactionDto: UpdateMemberTransactionDto, updatedBy?: string): Promise<MemberTransaction> {
    this.logger.info(`Updating transaction: ${id}`, 'MemberTransactionsService', { updateTransactionDto, updatedBy });

    try {
      const transaction = await this.findOne(id);

      // Update transaction
      Object.assign(transaction, updateTransactionDto, { updatedBy });
      
      const updatedTransaction = await this.transactionRepository.save(transaction);

      this.logger.info(
        `Transaction updated successfully: ${updatedTransaction.id}`,
        'MemberTransactionsService',
        { transactionId: id }
      );

      return updatedTransaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to update transaction: ${id}`, error, 'MemberTransactionsService');
      throw error;
    }
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    this.logger.info(`Deleting transaction: ${id}`, 'MemberTransactionsService', { deletedBy });

    try {
      const transaction = await this.findOne(id);

      // Soft delete
      transaction.deletedBy = deletedBy;
      await this.transactionRepository.softDelete(id);

      this.logger.info(`Transaction deleted successfully: ${transaction.id}`, 'MemberTransactionsService', { transactionId: id });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to delete transaction: ${id}`, error, 'MemberTransactionsService');
      throw error;
    }
  }

  async processTransaction(id: string, updatedBy?: string): Promise<MemberTransaction> {
    // TODO: Fix this method to match new MemberTransaction entity structure
    throw new Error('Method not implemented - needs to be updated for new MemberTransaction entity');
  }

  async findByMember(memberId: string, paginationDto: PaginationDto): Promise<PaginatedResult<MemberTransaction>> {
    this.logger.info(`Fetching transactions for member: ${memberId}`, 'MemberTransactionsService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
        .leftJoinAndSelect('transaction.member', 'member')
        .where('transaction.memberId = :memberId', { memberId });

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`transaction.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('transaction.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [transactions, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${transactions.length} transactions for member: ${memberId}`,
        'MemberTransactionsService',
        { total, page, limit, memberId }
      );

      return createPaginatedResult(transactions, total, page, limit);
    } catch (error) {
      this.logger.error(`Failed to fetch transactions for member: ${memberId}`, error, 'MemberTransactionsService');
      throw error;
    }
  }

  /**
   * Bulk create member transactions with optimized performance
   */
  async bulkCreate(bulkCreateDto: BulkCreateMemberTransactionDto, createdBy?: string): Promise<BulkCreateMemberTransactionResponseDto> {
    this.logger.info(
      `Starting bulk create for ${bulkCreateDto.transactions.length} member transactions`,
      'MemberTransactionsService',
      { count: bulkCreateDto.transactions.length, createdBy }
    );

    const response: BulkCreateMemberTransactionResponseDto = {
      success: 0,
      failed: 0,
      total: bulkCreateDto.transactions.length,
      errors: [],
      createdIds: [],
    };

    // Use transaction for better performance and data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get unique member IDs to validate they exist
      const memberIds = [...new Set(bulkCreateDto.transactions.map(t => t.member_id))];
      const existingMembers = await queryRunner.manager.find(Member, {
        where: memberIds.map(id => ({ id })),
        select: ['id'],
      });

      const existingMemberIds = existingMembers.map(m => m.id);

      // Filter valid transactions
      const validTransactions = bulkCreateDto.transactions.filter(transaction => {
        const isMemberValid = existingMemberIds.includes(transaction.member_id);

        if (!isMemberValid) {
          response.errors.push(`Member ID ${transaction.member_id} not found`);
          response.failed++;
          return false;
        }

        return true;
      });

      if (validTransactions.length > 0) {
        // Prepare transactions data for bulk insert
        const transactionsToCreate = validTransactions.map(transactionData => {
          const transactionEntity: any = {
            member_id: transactionData.member_id,
            type: transactionData.type,
            amount: transactionData.amount,
            fee: transactionData.fee || 0,
            status: transactionData.status || TransactionStatus.PENDING,
            createdBy,
          };

          if (transactionData.description) {
            transactionEntity.description = transactionData.description;
          }
          if (transactionData.reference_number) {
            transactionEntity.reference_number = transactionData.reference_number;
          }
          if (transactionData.transaction_data) {
            transactionEntity.transaction_data = transactionData.transaction_data;
          }

          return transactionEntity;
        });

        // Use bulk insert for better performance
        const insertResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(MemberTransaction)
          .values(transactionsToCreate)
          .execute();

        response.success = validTransactions.length;
        response.createdIds = insertResult.identifiers.map(identifier => identifier.id);

        this.logger.info(
          `Successfully bulk created ${response.success} member transactions`,
          'MemberTransactionsService',
          { success: response.success, failed: response.failed }
        );
      }

      await queryRunner.commitTransaction();

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        'Failed to bulk create member transactions',
        error,
        'MemberTransactionsService'
      );

      // If bulk insert fails, add error to response
      response.failed = bulkCreateDto.transactions.length;
      response.success = 0;
      response.errors.push(`Bulk insert failed: ${error.message}`);

    } finally {
      await queryRunner.release();
    }

    return response;
  }
}
