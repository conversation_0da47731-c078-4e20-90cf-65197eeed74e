import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FileDownloadController } from './file-download.controller';
import { FileDownloadService } from './file-download.service';
// AuditLog entity removed

@Module({
  imports: [
    // AuditLog entity removed
  ],
  controllers: [FileDownloadController],
  providers: [FileDownloadService],
  exports: [FileDownloadService],
})
export class FileDownloadModule {}
