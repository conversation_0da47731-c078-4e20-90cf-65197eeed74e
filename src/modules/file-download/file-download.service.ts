import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
// AuditLog entity removed
import { CustomLoggerService } from '@/common/logger/logger.service';
import * as fs from 'fs';
import * as path from 'path';
import * as mime from 'mime-types';

export interface DownloadOptions {
  customFilename?: string;
  autoDelete?: boolean;
}

export interface DownloadResult {
  filename: string;
  fileSize: number;
  mimeType: string;
  fileBuffer: Buffer;
}

export interface FileInfo {
  exists: boolean;
  filename: string;
  fileSize: number;
  mimeType: string;
  lastModified: Date;
  isAccessible: boolean;
}

export interface CleanupResult {
  deletedCount: number;
  deletedFiles: string[];
  errors: string[];
}

@Injectable()
export class FileDownloadService {
  private readonly allowedDirectories = [
    'uploads/exports',
    'uploads/temp',
    'uploads/reports',
  ];

  private readonly maxFileSize = 100 * 1024 * 1024; // 100MB

  constructor(
    // AuditLog repository removed
    private readonly logger: CustomLoggerService,
  ) {}

  /**
   * Download file and optionally delete it
   */
  async downloadFile(filePath: string, userId: string, options: DownloadOptions = {}): Promise<DownloadResult> {
    const { customFilename, autoDelete = true } = options;

    try {
      // Validate and sanitize file path
      const sanitizedPath = this.validateAndSanitizePath(filePath);
      const fullPath = path.resolve(sanitizedPath);

      // Check if file exists
      if (!fs.existsSync(fullPath)) {
        throw new NotFoundException(`File not found: ${filePath}`);
      }

      // Check file access permissions
      this.checkFileAccess(fullPath);

      // Get file stats
      const stats = fs.statSync(fullPath);
      
      // Check file size limit
      if (stats.size > this.maxFileSize) {
        throw new BadRequestException(`File too large: ${stats.size} bytes (max: ${this.maxFileSize} bytes)`);
      }

      // Read file content
      const fileBuffer = fs.readFileSync(fullPath);
      
      // Determine filename
      const originalFilename = path.basename(fullPath);
      const filename = customFilename || originalFilename;
      
      // Determine MIME type
      const mimeType = mime.lookup(fullPath) || 'application/octet-stream';

      // Create audit log
      await this.createAuditLog(userId, 'DOWNLOAD', filePath, `Downloaded file: ${filename} (${stats.size} bytes)`);

      this.logger.log(
        `File downloaded: ${filePath} by user ${userId} (${stats.size} bytes)`,
        'FileDownloadService'
      );

      return {
        filename,
        fileSize: stats.size,
        mimeType,
        fileBuffer,
      };

    } catch (error) {
      this.logger.error(
        `Failed to download file ${filePath}: ${error.message}`,
        error.stack,
        'FileDownloadService'
      );
      throw error;
    }
  }

  /**
   * Delete file from server
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      const sanitizedPath = this.validateAndSanitizePath(filePath);
      const fullPath = path.resolve(sanitizedPath);

      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
        this.logger.log(`File deleted: ${filePath}`, 'FileDownloadService');
      }
    } catch (error) {
      this.logger.error(
        `Failed to delete file ${filePath}: ${error.message}`,
        error.stack,
        'FileDownloadService'
      );
      throw error;
    }
  }

  /**
   * Get file information
   */
  async getFileInfo(filePath: string, userId: string): Promise<FileInfo> {
    try {
      const sanitizedPath = this.validateAndSanitizePath(filePath);
      const fullPath = path.resolve(sanitizedPath);

      const exists = fs.existsSync(fullPath);
      
      if (!exists) {
        return {
          exists: false,
          filename: path.basename(filePath),
          fileSize: 0,
          mimeType: 'unknown',
          lastModified: new Date(),
          isAccessible: false,
        };
      }

      const stats = fs.statSync(fullPath);
      const filename = path.basename(fullPath);
      const mimeType = mime.lookup(fullPath) || 'application/octet-stream';
      
      let isAccessible = true;
      try {
        this.checkFileAccess(fullPath);
      } catch {
        isAccessible = false;
      }

      // Create audit log for file info access
      await this.createAuditLog(userId, 'FILE_INFO', filePath, `Accessed file info: ${filename}`);

      return {
        exists: true,
        filename,
        fileSize: stats.size,
        mimeType,
        lastModified: stats.mtime,
        isAccessible,
      };

    } catch (error) {
      this.logger.error(
        `Failed to get file info ${filePath}: ${error.message}`,
        error.stack,
        'FileDownloadService'
      );
      throw error;
    }
  }

  /**
   * Cleanup old temporary files
   */
  async cleanupOldFiles(olderThanHours: number, userId: string): Promise<CleanupResult> {
    const result: CleanupResult = {
      deletedCount: 0,
      deletedFiles: [],
      errors: [],
    };

    try {
      const cutoffTime = new Date(Date.now() - (olderThanHours * 60 * 60 * 1000));
      
      // Cleanup directories
      const cleanupDirs = ['uploads/temp', 'uploads/exports'];
      
      for (const dir of cleanupDirs) {
        if (!fs.existsSync(dir)) {
          continue;
        }

        const files = fs.readdirSync(dir);
        
        for (const file of files) {
          const filePath = path.join(dir, file);
          
          try {
            const stats = fs.statSync(filePath);
            
            if (stats.mtime < cutoffTime) {
              fs.unlinkSync(filePath);
              result.deletedCount++;
              result.deletedFiles.push(filePath);
              
              this.logger.log(`Cleaned up old file: ${filePath}`, 'FileDownloadService');
            }
          } catch (error) {
            result.errors.push(`Failed to cleanup ${filePath}: ${error.message}`);
          }
        }
      }

      // Create audit log
      await this.createAuditLog(
        userId, 
        'CLEANUP', 
        'bulk', 
        `Cleaned up ${result.deletedCount} old files (older than ${olderThanHours}h)`
      );

      this.logger.log(
        `Cleanup completed: ${result.deletedCount} files deleted, ${result.errors.length} errors`,
        'FileDownloadService'
      );

      return result;

    } catch (error) {
      this.logger.error(
        `Cleanup failed: ${error.message}`,
        error.stack,
        'FileDownloadService'
      );
      throw error;
    }
  }

  /**
   * Validate and sanitize file path
   */
  private validateAndSanitizePath(filePath: string): string {
    if (!filePath || typeof filePath !== 'string') {
      throw new BadRequestException('Invalid file path');
    }

    // Remove any path traversal attempts
    const sanitized = path.normalize(filePath).replace(/^(\.\.[\/\\])+/, '');
    
    // Check if path is in allowed directories
    const isAllowed = this.allowedDirectories.some(allowedDir => 
      sanitized.startsWith(allowedDir)
    );

    if (!isAllowed) {
      throw new ForbiddenException(`Access denied to path: ${filePath}`);
    }

    return sanitized;
  }

  /**
   * Check file access permissions
   */
  private checkFileAccess(fullPath: string): void {
    try {
      // Check if file is readable
      fs.accessSync(fullPath, fs.constants.R_OK);
    } catch (error) {
      throw new ForbiddenException(`Access denied to file: ${fullPath}`);
    }
  }

  /**
   * Create audit log entry
   */
  private async createAuditLog(userId: string, action: string, entityId: string, description: string): Promise<void> {
    // Audit log functionality removed
    this.logger.info(`Audit log would be created: ${action} for ${entityId}`, 'FileDownloadService');
  }
}
