import {
  <PERSON>,
  Get,
  Query,
  Res,
  UseGuards,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, Api<PERSON><PERSON>erAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { RoleType } from '@/common/constants';
import { PermissionsType } from '@/common/constants';
import { User } from '@/entities';
import { FileDownloadService } from './file-download.service';

@ApiTags('File Download')
@ApiBearerAuth('JWT-auth')
@Controller('download')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class FileDownloadController {
  constructor(private readonly fileDownloadService: FileDownloadService) {}

  @Get()
  @ApiOperation({ 
    summary: 'Download file by path and auto-delete after download',
    description: 'Downloads a file from server and automatically deletes it after successful download. Supports export files, temporary files, etc.'
  })
  @ApiQuery({
    name: 'path',
    description: 'File path relative to server root (e.g., uploads/exports/file.xlsx)',
    example: 'uploads/exports/ftd-export-2025-08-01T03-41-17-901Z.xlsx'
  })
  @ApiQuery({
    name: 'filename',
    description: 'Custom filename for download (optional)',
    required: false,
    example: 'my-export-data.xlsx'
  })
  @ApiQuery({
    name: 'autoDelete',
    description: 'Auto delete file after download (default: true)',
    required: false,
    example: true
  })
  @ApiResponse({ 
    status: 200, 
    description: 'File downloaded successfully and deleted from server',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid file path' })
  @ApiResponse({ status: 403, description: 'Access denied to file' })
  @ApiResponse({ status: 404, description: 'File not found' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async downloadFile(
    @Query('path') filePath: string,
    @CurrentUser() currentUser: User,
    @Res() res: Response,
    @Query('filename') customFilename?: string,
    @Query('autoDelete') autoDelete: string = 'true',
  ) {
    if (!filePath) {
      throw new BadRequestException('File path is required');
    }

    const shouldAutoDelete = autoDelete === 'true' || autoDelete === '1';

    try {
      const downloadResult = await this.fileDownloadService.downloadFile(
        filePath,
        currentUser.id,
        {
          customFilename,
          autoDelete: shouldAutoDelete,
        }
      );

      // Set response headers
      res.setHeader('Content-Type', downloadResult.mimeType);
      res.setHeader('Content-Length', downloadResult.fileSize);
      res.setHeader('Content-Disposition', `attachment; filename="${downloadResult.filename}"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      // Send file content
      res.send(downloadResult.fileBuffer);

      // Auto delete file if requested (after response is sent)
      if (shouldAutoDelete) {
        // Use setImmediate to ensure response is sent before deletion
        setImmediate(async () => {
          try {
            await this.fileDownloadService.deleteFile(filePath);
          } catch (deleteError) {
            // Log error but don't throw since response is already sent
            console.error(`Failed to delete file after download: ${deleteError.message}`);
          }
        });
      }

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(`Download failed: ${error.message}`);
    }
  }

  @Get('info')
  @ApiOperation({ 
    summary: 'Get file information without downloading',
    description: 'Returns file metadata including size, type, and existence status'
  })
  @ApiQuery({
    name: 'path',
    description: 'File path relative to server root',
    example: 'uploads/exports/ftd-export-2025-08-01T03-41-17-901Z.xlsx'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'File information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        exists: { type: 'boolean' },
        filename: { type: 'string' },
        fileSize: { type: 'number' },
        mimeType: { type: 'string' },
        lastModified: { type: 'string', format: 'date-time' },
        isAccessible: { type: 'boolean' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid file path' })
  @ApiResponse({ status: 404, description: 'File not found' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async getFileInfo(
    @Query('path') filePath: string,
    @CurrentUser() currentUser: User,
  ) {
    if (!filePath) {
      throw new BadRequestException('File path is required');
    }

    try {
      const fileInfo = await this.fileDownloadService.getFileInfo(filePath, currentUser.id);
      return fileInfo;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new BadRequestException(`Failed to get file info: ${error.message}`);
    }
  }

  @Get('cleanup')
  @ApiOperation({ 
    summary: 'Cleanup old temporary files',
    description: 'Removes temporary files older than specified hours (admin only)'
  })
  @ApiQuery({
    name: 'olderThanHours',
    description: 'Delete files older than this many hours (default: 24)',
    required: false,
    example: 24
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Cleanup completed successfully',
    schema: {
      type: 'object',
      properties: {
        deletedCount: { type: 'number' },
        deletedFiles: { type: 'array', items: { type: 'string' } },
        errors: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  async cleanupOldFiles(
    @Query('olderThanHours') olderThanHours: string = '24',
    @CurrentUser() currentUser: User,
  ) {
    const hours = parseInt(olderThanHours) || 24;
    
    try {
      const cleanupResult = await this.fileDownloadService.cleanupOldFiles(hours, currentUser.id);
      return cleanupResult;
    } catch (error) {
      throw new BadRequestException(`Cleanup failed: ${error.message}`);
    }
  }
}
