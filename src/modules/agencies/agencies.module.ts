import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Agency } from '@/entities/agency.entity';
import { AgenciesService } from './agencies.service';
import { AgenciesController } from './agencies.controller';
import { CommonServicesModule } from '@/common/services/common-services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Agency]),
    CommonServicesModule,
  ],
  controllers: [AgenciesController],
  providers: [AgenciesService],
  exports: [AgenciesService],
})
export class AgenciesModule {}
