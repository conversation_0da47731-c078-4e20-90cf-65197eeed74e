import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class AgencyResponseDto {
  @ApiProperty({
    description: 'Agency ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Name of the agency',
    example: 'ABC Marketing Agency',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Description of the agency',
    example: 'Full-service marketing agency',
  })
  @Expose()
  description?: string;

  @ApiPropertyOptional({
    description: 'Agency contact email',
    example: '<EMAIL>',
  })
  @Expose()
  email?: string;

  @ApiPropertyOptional({
    description: 'Agency contact phone',
    example: '+1234567890',
  })
  @Expose()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Agency address',
    example: '123 Main St, City, State 12345',
  })
  @Expose()
  address?: string;

  @ApiProperty({
    description: 'Whether the agency is active',
    example: true,
  })
  @Expose()
  active: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'User who created this record',
    example: 'admin',
  })
  @Expose()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'User who last updated this record',
    example: 'admin',
  })
  @Expose()
  updatedBy?: string;
}
