import { IsString, <PERSON>Optional, IsBoolean, IsEmail, Max<PERSON>ength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateAgencyDto {
  @ApiProperty({
    description: 'Brand ID associated with the agency',
    example: '1',
  })
  @IsString()
  brand_id: string;

  @ApiProperty({
    description: 'Unique agency code',
    example: 'AG001',
    maxLength: 100,
  })
  @IsString()
  @MaxLength(100)
  code: string;

  @ApiProperty({
    description: 'Name of the agency',
    example: 'ABC Marketing Agency',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: 'Agency contact email',
    example: '<EMAIL>',
    maxLength: 255,
  })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiPropertyOptional({
    description: 'Agency contact phone',
    example: '+1234567890',
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  phone?: string;

  @ApiPropertyOptional({
    description: 'Agency address',
    example: '123 Main St, City, State 12345',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: 'Extra information about the agency',
    example: 'Additional agency details',
  })
  @IsOptional()
  @IsString()
  extra?: string;

  @ApiPropertyOptional({
    description: 'Description of the agency',
    example: 'Full-service marketing agency',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Whether the agency is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean;
}
