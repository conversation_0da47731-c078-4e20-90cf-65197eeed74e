import { IsArray, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateAgencyDto } from './create-agency.dto';

export class BulkCreateAgencyDto {
  @ApiProperty({
    description: 'Array of agencies to create',
    type: [CreateAgencyDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAgencyDto)
  agencies: CreateAgencyDto[];
}

export class BulkCreateAgencyResponseDto {
  @ApiProperty({
    description: 'Number of successfully created agencies',
    example: 95,
  })
  success: number;

  @ApiProperty({
    description: 'Number of failed agency creations',
    example: 5,
  })
  failed: number;

  @ApiProperty({
    description: 'Total number of agencies processed',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Array of error messages for failed creations',
    type: [String],
    example: ['Agency code AG001 already exists', 'Invalid brand ID 999'],
  })
  errors: string[];

  @ApiProperty({
    description: 'Array of successfully created agency IDs',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-426614174000', '456e7890-e89b-12d3-a456-426614174001'],
  })
  createdIds: string[];
}
