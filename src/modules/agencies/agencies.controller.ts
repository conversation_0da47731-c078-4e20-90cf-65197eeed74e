import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { AgenciesService } from './agencies.service';
import { CreateAgencyDto, UpdateAgencyDto, AgencyResponseDto, BulkCreateAgencyDto, BulkCreateAgencyResponseDto } from './dto';
import { PaginationDto, PaginatedResult } from '@/common/dto/pagination.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { plainToClass } from 'class-transformer';

@ApiTags('Agencies')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('agencies')
export class AgenciesController {
  constructor(private readonly agenciesService: AgenciesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new agency' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Agency created successfully',
    type: AgencyResponseDto,
  })
  async create(
    @Body() createAgencyDto: CreateAgencyDto,
    @Request() req: any,
  ): Promise<AgencyResponseDto> {
    const agency = await this.agenciesService.create(createAgencyDto, req.user?.username);
    return plainToClass(AgencyResponseDto, agency, { excludeExtraneousValues: true });
  }

  @Post('bulk')
  @ApiOperation({ summary: 'Bulk create multiple agencies' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Agencies bulk created successfully',
    type: BulkCreateAgencyResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async bulkCreate(
    @Body() bulkCreateAgencyDto: BulkCreateAgencyDto,
    @Request() req: any,
  ): Promise<BulkCreateAgencyResponseDto> {
    return await this.agenciesService.bulkCreate(
      bulkCreateAgencyDto,
      req.user?.username
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all agencies with pagination' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Agencies retrieved successfully',
    type: [AgencyResponseDto],
  })
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResult<AgencyResponseDto>> {
    const result = await this.agenciesService.findAll(paginationDto);
    
    return {
      ...result,
      data: result.data.map(agency => 
        plainToClass(AgencyResponseDto, agency, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an agency by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Agency retrieved successfully',
    type: AgencyResponseDto,
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<AgencyResponseDto> {
    const agency = await this.agenciesService.findOne(id);
    return plainToClass(AgencyResponseDto, agency, { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an agency' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Agency updated successfully',
    type: AgencyResponseDto,
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAgencyDto: UpdateAgencyDto,
    @Request() req: any,
  ): Promise<AgencyResponseDto> {
    const agency = await this.agenciesService.update(id, updateAgencyDto, req.user?.username);
    return plainToClass(AgencyResponseDto, agency, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an agency' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Agency deleted successfully',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.agenciesService.remove(id, req.user?.username);
  }

  @Patch(':id/toggle-active')
  @ApiOperation({ summary: 'Toggle agency active status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Agency active status toggled successfully',
    type: AgencyResponseDto,
  })
  async toggleActive(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<AgencyResponseDto> {
    const agency = await this.agenciesService.toggleActive(id, req.user?.username);
    return plainToClass(AgencyResponseDto, agency, { excludeExtraneousValues: true });
  }
}
