import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Department } from '@/entities/department.entity';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class DepartmentsService {
  constructor(
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createDepartmentDto: CreateDepartmentDto, createdBy?: string): Promise<Department> {
    this.logger.info(
      `Creating new department: ${createDepartmentDto.name}`,
      'DepartmentsService',
      { name: createDepartmentDto.name, createdBy }
    );

    // Check if department name already exists
    const existingDepartment = await this.departmentRepository.findOne({
      where: { name: createDepartmentDto.name },
    });
    if (existingDepartment) {
      this.logger.warn(
        `Department creation failed - name already exists: ${createDepartmentDto.name}`,
        'DepartmentsService'
      );
      throw new ConflictException('Department name already exists');
    }

    try {
      // Create department
      const department = this.departmentRepository.create({
        ...createDepartmentDto,
        createdBy,
      });

      const savedDepartment = await this.departmentRepository.save(department);

      this.logger.info(
        `Department created successfully: ${savedDepartment.name}`,
        'DepartmentsService',
        { departmentId: savedDepartment.id, name: savedDepartment.name }
      );

      return savedDepartment;
    } catch (error) {
      this.logger.error(
        `Failed to create department: ${createDepartmentDto.name}`,
        error.stack,
        'DepartmentsService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<Department>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;
    
    const queryBuilder = this.departmentRepository.createQueryBuilder('department')
      .leftJoinAndSelect('department.teams', 'teams');
    
    // Add search functionality
    if (search) {
      queryBuilder.where('department.name LIKE :search', { search: `%${search}%` });
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`department.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [departments, total] = await queryBuilder.getManyAndCount();

    return createPaginatedResult(departments, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<Department> {
    const department = await this.departmentRepository.findOne({
      where: { id },
      relations: ['teams'],
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return department;
  }

  async findByName(name: string): Promise<Department | null> {
    return await this.departmentRepository.findOne({
      where: { name },
      relations: ['teams'],
    });
  }

  async update(id: string, updateDepartmentDto: UpdateDepartmentDto, updatedBy?: string): Promise<Department> {
    const department = await this.findOne(id);

    // Check if name is being updated and already exists
    if (updateDepartmentDto.name && updateDepartmentDto.name !== department.name) {
      const existingDepartment = await this.departmentRepository.findOne({
        where: { name: updateDepartmentDto.name },
      });
      if (existingDepartment) {
        throw new ConflictException('Department name already exists');
      }
    }

    // Update department
    Object.assign(department, updateDepartmentDto, { updatedBy });
    return await this.departmentRepository.save(department);
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    const department = await this.findOne(id);
    
    // Check if department has teams
    if (department.teams && department.teams.length > 0) {
      throw new ConflictException('Cannot delete department with existing teams');
    }
    
    // Soft delete
    department.deletedBy = deletedBy;
    await this.departmentRepository.softRemove(department);
  }

  async getDepartmentStats(id: string): Promise<{
    totalTeams: number;
    totalUsers: number;
  }> {
    const department = await this.departmentRepository
      .createQueryBuilder('department')
      .leftJoinAndSelect('department.teams', 'teams')
      .leftJoinAndSelect('department.users', 'users')
      .where('department.id = :id', { id })
      .getOne();

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return {
      totalTeams: department.teams?.length || 0,
      totalUsers: department.users?.length || 0,
    };
  }
}
