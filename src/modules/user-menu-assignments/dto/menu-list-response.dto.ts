import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SubMenuListDto {
  @ApiProperty({
    description: 'SubMenu ID',
    example: '5e8817d8-9175-4fb9-b647-7686f00e0356',
  })
  id: string;

  @ApiProperty({
    description: 'SubMenu title',
    example: 'Sale Khu A',
  })
  title: string;

  @ApiPropertyOptional({
    description: 'SubMenu description',
    example: null,
  })
  description?: string;

  @ApiProperty({
    description: 'SubMenu link',
    example: '/sale-khu-a',
  })
  link: string;
}

export class MenuListDto {
  @ApiProperty({
    description: 'Menu ID',
    example: '089b77e7-9b5a-42d0-bc17-96e698ea4c1c',
  })
  id: string;

  @ApiProperty({
    description: 'Menu title',
    example: 'Doanh Thu',
  })
  title: string;

  @ApiProperty({
    description: 'Menu description',
    example: 'Menu Do<PERSON>h Thu',
  })
  description: string;

  @ApiProperty({
    description: 'Menu submenus',
    type: [SubMenuListDto],
  })
  submenus: SubMenuListDto[];
}

export class MenuListResponseDto {
  @ApiProperty({
    description: 'List of menus with submenus',
    type: [MenuListDto],
  })
  menus: MenuListDto[];
}
