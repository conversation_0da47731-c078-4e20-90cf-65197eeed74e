import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UserMenuAssignmentResponseDto {
  @ApiProperty({
    description: 'Assignment ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userId: string;

  @ApiProperty({
    description: 'Menu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  menuId: string;

  @ApiPropertyOptional({
    description: 'SubMenu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  submenuId?: string;

  @ApiPropertyOptional({
    description: 'Department ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  departmentId?: string;
  
  @ApiPropertyOptional({
    description: 'Team ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  teamId?: string;

  @ApiProperty({
    description: 'Array of permissions for this menu/submenu assignment',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        description: { type: 'string' },
      },
    },
  })
  permissions: {
    id: string;
    name: string;
    description?: string;
  }[];

  @ApiProperty({
    description: 'User information',
  })
  user?: {
    id: string;
    username: string;
    fullName: string;
    email?: string;
  };

  @ApiProperty({
    description: 'Menu information',
  })
  menu?: {
    id: string;
    title: string;
    description?: string;
  };

  @ApiPropertyOptional({
    description: 'SubMenu information',
  })
  submenu?: {
    id: string;
    title: string;
    description?: string;
    link?: string;
  };

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Created by user',
    example: 'admin',
  })
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Updated by user',
    example: 'admin',
  })
  updatedBy?: string;
}
