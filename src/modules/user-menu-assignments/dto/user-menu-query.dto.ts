import { IsUUID, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationDto } from '@/common/dto/pagination.dto';

export class UserMenuQueryDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Filter by User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Filter by Menu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  menuId?: string;

  @ApiPropertyOptional({
    description: 'Filter by SubMenu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  submenuId?: string;

  @ApiPropertyOptional({
    description: 'Filter by Permission ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  permissionId?: string;

  @ApiPropertyOptional({
    description: 'Filter by Department ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  departmentId?: string;
  

  @ApiPropertyOptional({
    description: 'Filter by Team ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  teamId?: string;
}
