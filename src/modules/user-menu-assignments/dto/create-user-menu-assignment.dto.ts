import { IsUUID, IsOptional, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserMenuAssignmentDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Menu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  menuId: string;

  @ApiProperty({
    description: 'Department ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  departmentId: string;

  @ApiProperty({
    description: 'Team ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  teamId: string;

  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  roleId: string;

  @ApiPropertyOptional({
    description: 'SubMenu ID (optional for submenu-specific permissions)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  submenuId?: string;

  @ApiProperty({
    description: 'Array of Permission IDs for this menu/submenu assignment',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds: string[];
}
