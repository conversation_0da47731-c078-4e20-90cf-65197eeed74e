import { IsUUID, <PERSON><PERSON><PERSON>y, <PERSON>Optional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class MenuAssignmentDto {
  @ApiProperty({
    description: 'Menu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Array of permission IDs for this menu',
    type: [String],
    example: ['perm1-uuid', 'perm2-uuid'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds: string[];
}

export class SubMenuAssignmentDto {
  @ApiProperty({
    description: 'SubMenu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Array of permission IDs for this submenu',
    type: [String],
    example: ['perm1-uuid', 'perm2-uuid'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds: string[];
}

export class BulkAssignMenuDto {
  @ApiProperty({
    description: 'User ID to assign menus to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Department ID to assign menus to (optional)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiProperty({
    description: 'Team ID to assign menus to (optional)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  teamId?: string;

  @ApiProperty({
    description: 'Role ID to assign menus to (optional)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  roleId: string;

  @ApiPropertyOptional({
    description: 'Menu assignments with permissions',
    type: [MenuAssignmentDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MenuAssignmentDto)
  menu?: MenuAssignmentDto[];

  @ApiPropertyOptional({
    description: 'SubMenu assignments with permissions',
    type: [SubMenuAssignmentDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SubMenuAssignmentDto)
  subMenu?: SubMenuAssignmentDto[];
}
