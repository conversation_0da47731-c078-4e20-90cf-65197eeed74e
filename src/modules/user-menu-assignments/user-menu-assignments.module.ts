import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserMenuAssignmentsService } from './user-menu-assignments.service';
import { UserMenuAssignmentsController } from './user-menu-assignments.controller';
import { UserMenuAssignment } from '@/entities/user-menu-assignment.entity';
import { User } from '@/entities/user.entity';
import { Menu } from '@/entities/menu.entity';
import { SubMenu } from '@/entities/submenu.entity';
import { Permission } from '@/entities/permission.entity';
import { Department } from '@/entities/department.entity';
import { Team } from '@/entities/team.entity';
import { UserRoleAssignment } from '@/entities/user-role-assignment.entity';
import { Role } from '@/entities/role.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserMenuAssignment,
      User,
      Menu,
      SubMenu,
      Permission,
      Department,
      Team,
      UserRoleAssignment,
      Role,
    ]),
  ],
  controllers: [UserMenuAssignmentsController],
  providers: [UserMenuAssignmentsService],
  exports: [UserMenuAssignmentsService],
})
export class UserMenuAssignmentsModule {}
