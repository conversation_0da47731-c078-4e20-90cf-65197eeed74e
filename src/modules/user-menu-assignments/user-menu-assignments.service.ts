import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { UserMenuAssignment } from '@/entities/user-menu-assignment.entity';
import { User } from '@/entities/user.entity';
import { Menu } from '@/entities/menu.entity';
import { SubMenu } from '@/entities/submenu.entity';
import { Permission } from '@/entities/permission.entity';
import {
  UserMenuStructureResponseDto,
  MenuListResponseDto
} from './dto';
import { Department } from '@/entities/department.entity';
import { Team } from '@/entities/team.entity';
import { UserRoleAssignment } from '@/entities/user-role-assignment.entity';
import { Role } from '@/entities/role.entity';

@Injectable()
export class UserMenuAssignmentsService {
  constructor(
    @InjectRepository(UserMenuAssignment)
    private readonly userMenuAssignmentRepository: Repository<UserMenuAssignment>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Menu)
    private readonly menuRepository: Repository<Menu>,
    @InjectRepository(SubMenu)
    private readonly submenuRepository: Repository<SubMenu>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Team)
    private readonly teamRepository: Repository<Team>,
    @InjectRepository(UserRoleAssignment)
    private readonly userRoleAssignmentRepository: Repository<UserRoleAssignment>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  /**
   * Check if user has specific permission for a menu
   */
  async userHasMenuPermission(userId: string, menuId: string, permissionName: string): Promise<boolean> {
    const assignment = await this.userMenuAssignmentRepository.findOne({
      where: {
        userId,
        menuId,
        submenuId: IsNull(),
      },
      relations: ['permissions'],
    });

    if (!assignment) {
      return false;
    }

    return assignment.permissions.some(permission => permission.action === permissionName);
  }

  /**
   * Check if user has specific permission for a submenu
   */
  async userHasSubmenuPermission(userId: string, submenuId: string, permissionName: string): Promise<boolean> {
    const assignment = await this.userMenuAssignmentRepository.findOne({
      where: {
        userId,
        submenuId,
      },
      relations: ['permissions'],
    });

    if (!assignment) {
      return false;
    }

    return assignment.permissions.some(permission => permission.action === permissionName);
  }

  /**
   * Get user's menu permissions
   */
  async getUserMenuPermissions(userId: string, menuId: string): Promise<Permission[]> {
    const assignment = await this.userMenuAssignmentRepository.findOne({
      where: {
        userId,
        menuId,
        submenuId: IsNull(),
      },
      relations: ['permissions'],
    });

    return assignment ? assignment.permissions : [];
  }

  /**
   * Get user's complete menu structure with permissions
   */
  async getUserMenuStructure(userId: string): Promise<UserMenuStructureResponseDto> {
    // Validate user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['department', 'teams', 'role']
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get all user menu assignments with relations
    const assignments = await this.userMenuAssignmentRepository.find({
      where: { userId },
      relations: ['menu', 'submenu', 'permissions'],
      order: {
        menu: { title: 'ASC' },
        submenu: { title: 'ASC' }
      }
    });

    // Get user role assignments to get department, team, role info
    const roleAssignments = await this.userRoleAssignmentRepository.find({
      where: { userId }
    });

    // Build the response structure
    const menuMap = new Map();

    // Process assignments
    for (const assignment of assignments) {
      const menuId = assignment.menu.id;

      if (!menuMap.has(menuId)) {
        menuMap.set(menuId, {
          id: assignment.menu.id,
          title: assignment.menu.title,
          description: assignment.menu.description,
          permissions: [],
          submenus: []
        });
      }

      const menuData = menuMap.get(menuId);

      if (!assignment.submenuId) {
        // This is a menu-level assignment
        menuData.permissions = assignment.permissions.map(permission => ({
          id: permission.id,
          name: permission.name,
          action: permission.action,
          description: permission.description
        }));
      } else if (assignment.submenu) {
        // This is a submenu-level assignment
        const existingSubmenu = menuData.submenus.find(sub => sub.id === assignment.submenu!.id);
        if (!existingSubmenu) {
          menuData.submenus.push({
            id: assignment.submenu.id,
            title: assignment.submenu.title,
            description: assignment.submenu.description,
            link: assignment.submenu.link,
            permissions: assignment.permissions.map(permission => ({
              id: permission.id,
              name: permission.name,
              action: permission.action,
              description: permission.description
            }))
          });
        }
      }
    }

    // Get department, role, teams info from role assignments and user
    let department: { id: string; name: string } | undefined = undefined;
    let role: { id: string; name: string } | undefined = undefined;
    let teams: { id: string; name: string }[] = [];

    // First try to get from user's direct relations
    if (user.department) {
      department = {
        id: user.department.id,
        name: user.department.name
      };
    }

    if (user.role) {
      role = {
        id: user.role.id,
        name: user.role.name
      };
    }

    if (user.teams && user.teams.length > 0) {
      teams = user.teams.map(team => ({
        id: team.id,
        name: team.name
      }));
    }

    // If not found in user, try to get from role assignments
    if (roleAssignments.length > 0 && (!department || !role || teams.length === 0)) {
      const roleAssignment = roleAssignments[0]; // Take the first one

      if (!department && roleAssignment.departmentId) {
        const dept = await this.departmentRepository.findOne({
          where: { id: roleAssignment.departmentId }
        });
        if (dept) {
          department = {
            id: dept.id,
            name: dept.name
          };
        }
      }

      if (!role && roleAssignment.roleId) {
        const roleEntity = await this.roleRepository.findOne({
          where: { id: roleAssignment.roleId }
        });
        if (roleEntity) {
          role = {
            id: roleEntity.id,
            name: roleEntity.name
          };
        }
      }

      // Get teams from all role assignments
      if (teams.length === 0) {
        const teamIds = roleAssignments
          .filter(ra => ra.teamId)
          .map(ra => ra.teamId);

        if (teamIds.length > 0) {
          const teamEntities = await this.teamRepository.find({
            where: { id: In(teamIds) }
          });
          teams = teamEntities.map(team => ({
            id: team.id,
            name: team.name
          }));
        }
      }
    }

    return {
      department,
      role,
      teams,
      menus: Array.from(menuMap.values())
    };
  }

  /**
   * Get all menus with their submenus (no permissions)
   */
  async getMenuList(): Promise<MenuListResponseDto> {
    // Get all menus with their submenus
    const menus = await this.menuRepository.find({
      relations: ['submenus'],
      order: {
        title: 'ASC',
        submenus: { title: 'ASC' }
      }
    });

    // Transform to response format
    const menuList = menus.map(menu => ({
      id: menu.id,
      title: menu.title,
      description: menu.description,
      submenus: menu.submenus.map(submenu => ({
        id: submenu.id,
        title: submenu.title,
        description: submenu.description,
        link: submenu.link
      }))
    }));

    return {
      menus: menuList
    };
  }
}
