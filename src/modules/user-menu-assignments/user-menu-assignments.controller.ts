import {
  Controller,
  Get,
  Param,
  UseGuards,
  HttpStatus,
  ParseUUI<PERSON>ipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserMenuAssignmentsService } from './user-menu-assignments.service';
import {
  UserMenuStructureResponseDto,
  MenuListResponseDto,
} from './dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { RoleType, PermissionsType } from '@/common/constants';

@ApiTags('User Menu Assignments')
@Controller('user-menu-assignments')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class UserMenuAssignmentsController {
  constructor(private readonly userMenuAssignmentsService: UserMenuAssignmentsService) {}

  @Get('menus')
  @ApiOperation({ summary: 'Get all menus with submenus' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Menu list retrieved successfully',
    type: MenuListResponseDto,
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async getMenuList(): Promise<MenuListResponseDto> {
    return await this.userMenuAssignmentsService.getMenuList();
  }

  @Get('user/:userId/menu/:menuId/permissions')
  @ApiOperation({ summary: 'Get user permissions for a specific menu' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiParam({ name: 'menuId', description: 'Menu ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Menu permissions retrieved successfully',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async getUserMenuPermissions(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('menuId', ParseUUIDPipe) menuId: string,
  ) {
    return this.userMenuAssignmentsService.getUserMenuPermissions(userId, menuId);
  }

  @Get('check/user/:userId/menu/:menuId/permission/:permissionName')
  @ApiOperation({ summary: 'Check if user has specific permission for a menu' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiParam({ name: 'menuId', description: 'Menu ID', type: 'string' })
  @ApiParam({ name: 'permissionName', description: 'Permission name', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission check completed',
    schema: { type: 'object', properties: { hasPermission: { type: 'boolean' } } },
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async checkUserMenuPermission(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('menuId', ParseUUIDPipe) menuId: string,
    @Param('permissionName') permissionName: string,
  ) {
    const hasPermission = await this.userMenuAssignmentsService.userHasMenuPermission(userId, menuId, permissionName);
    return { hasPermission };
  }

  @Get('check/user/:userId/submenu/:submenuId/permission/:permissionName')
  @ApiOperation({ summary: 'Check if user has specific permission for a submenu' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiParam({ name: 'submenuId', description: 'SubMenu ID', type: 'string' })
  @ApiParam({ name: 'permissionName', description: 'Permission name', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission check completed',
    schema: { type: 'object', properties: { hasPermission: { type: 'boolean' } } },
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async checkUserSubmenuPermission(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('submenuId', ParseUUIDPipe) submenuId: string,
    @Param('permissionName') permissionName: string,
  ) {
    const hasPermission = await this.userMenuAssignmentsService.userHasSubmenuPermission(userId, submenuId, permissionName);
    return { hasPermission };
  }

  @Get('user/:userId/menu-structure')
  @ApiOperation({ summary: 'Get user menu structure with permissions' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User menu structure retrieved successfully',
    type: UserMenuStructureResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async getUserMenuStructure(@Param('userId', ParseUUIDPipe) userId: string): Promise<UserMenuStructureResponseDto> {
    return await this.userMenuAssignmentsService.getUserMenuStructure(userId);
  }

}
