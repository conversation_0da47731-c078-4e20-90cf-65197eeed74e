import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Brand } from '@/entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { UpdateBrandDto } from './dto/update-brand.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class BrandsService {
  constructor(
    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createBrandDto: CreateBrandDto, createdBy?: string): Promise<Brand> {
    this.logger.info(
      `Creating new brand: ${createBrandDto.name}`,
      'BrandsService',
      { name: createBrandDto.name, createdBy }
    );

    // Check if brand name already exists
    const existingBrand = await this.brandRepository.findOne({
      where: { name: createBrandDto.name },
    });
    if (existingBrand) {
      this.logger.warn(
        `Brand creation failed - name already exists: ${createBrandDto.name}`,
        'BrandsService'
      );
      throw new ConflictException('Brand name already exists');
    }

    try {
      // Create brand
      const brand = this.brandRepository.create({
        ...createBrandDto,
        createdBy,
      });

      const savedBrand = await this.brandRepository.save(brand);

      this.logger.info(
        `Brand created successfully: ${savedBrand.name}`,
        'BrandsService',
        { brandId: savedBrand.id, name: savedBrand.name }
      );

      return savedBrand;
    } catch (error) {
      this.logger.error(
        `Failed to create brand: ${createBrandDto.name}`,
        error,
        'BrandsService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<Brand>> {
    this.logger.info('Fetching all brands', 'BrandsService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.brandRepository.createQueryBuilder('brand');

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`brand.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('brand.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [brands, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${brands.length} brands`,
        'BrandsService',
        { total, page, limit }
      );

      return createPaginatedResult(brands, total, page, limit);
    } catch (error) {
      this.logger.error('Failed to fetch brands', error, 'BrandsService');
      throw error;
    }
  }

  async findOne(id: string): Promise<Brand> {
    this.logger.info(`Fetching brand by ID: ${id}`, 'BrandsService');

    try {
      const brand = await this.brandRepository.findOne({
        where: { id },
        relations: ['proposals'],
      });

      if (!brand) {
        this.logger.warn(`Brand not found: ${id}`, 'BrandsService');
        throw new NotFoundException('Brand not found');
      }

      this.logger.info(`Brand found: ${brand.name}`, 'BrandsService', { brandId: id });
      return brand;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch brand: ${id}`, error, 'BrandsService');
      throw error;
    }
  }

  async update(id: string, updateBrandDto: UpdateBrandDto, updatedBy?: string): Promise<Brand> {
    this.logger.info(`Updating brand: ${id}`, 'BrandsService', { updateBrandDto, updatedBy });

    try {
      const brand = await this.findOne(id);

      // Check if new name conflicts with existing brand (if name is being updated)
      if (updateBrandDto.name && updateBrandDto.name !== brand.name) {
        const existingBrand = await this.brandRepository.findOne({
          where: { name: updateBrandDto.name },
        });
        if (existingBrand) {
          this.logger.warn(
            `Brand update failed - name already exists: ${updateBrandDto.name}`,
            'BrandsService'
          );
          throw new ConflictException('Brand name already exists');
        }
      }

      // Update brand
      Object.assign(brand, updateBrandDto, { updatedBy });
      const updatedBrand = await this.brandRepository.save(brand);

      this.logger.info(
        `Brand updated successfully: ${updatedBrand.name}`,
        'BrandsService',
        { brandId: id }
      );

      return updatedBrand;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Failed to update brand: ${id}`, error, 'BrandsService');
      throw error;
    }
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    this.logger.info(`Deleting brand: ${id}`, 'BrandsService', { deletedBy });

    try {
      const brand = await this.findOne(id);

      // Soft delete
      brand.deletedBy = deletedBy;
      await this.brandRepository.softDelete(id);

      this.logger.info(`Brand deleted successfully: ${brand.name}`, 'BrandsService', { brandId: id });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to delete brand: ${id}`, error, 'BrandsService');
      throw error;
    }
  }

  async findByName(name: string): Promise<Brand | null> {
    this.logger.info(`Finding brand by name: ${name}`, 'BrandsService');

    try {
      const brand = await this.brandRepository.findOne({
        where: { name },
      });

      return brand;
    } catch (error) {
      this.logger.error(`Failed to find brand by name: ${name}`, error, 'BrandsService');
      throw error;
    }
  }

  async toggleActive(id: string, updatedBy?: string): Promise<Brand> {
    this.logger.info(`Toggling brand active status: ${id}`, 'BrandsService', { updatedBy });

    try {
      const brand = await this.findOne(id);
      brand.active = !brand.active;
      brand.updatedBy = updatedBy;

      const updatedBrand = await this.brandRepository.save(brand);

      this.logger.info(
        `Brand active status toggled: ${updatedBrand.name} - ${updatedBrand.active}`,
        'BrandsService',
        { brandId: id, active: updatedBrand.active }
      );

      return updatedBrand;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to toggle brand active status: ${id}`, error, 'BrandsService');
      throw error;
    }
  }
}
