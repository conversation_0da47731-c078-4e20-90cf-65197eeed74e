import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class BrandResponseDto {
  @ApiProperty({
    description: 'Brand ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Name of the brand',
    example: 'Nike',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Description of the brand',
    example: 'Leading sportswear brand',
  })
  @Expose()
  description?: string;

  @ApiPropertyOptional({
    description: 'Brand logo URL or path',
    example: 'https://example.com/logo.png',
  })
  @Expose()
  logo?: string;

  @ApiPropertyOptional({
    description: 'Brand website URL',
    example: 'https://www.nike.com',
  })
  @Expose()
  website?: string;

  @ApiProperty({
    description: 'Whether the brand is active',
    example: true,
  })
  @Expose()
  active: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'User who created this record',
    example: 'admin',
  })
  @Expose()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'User who last updated this record',
    example: 'admin',
  })
  @Expose()
  updatedBy?: string;
}
