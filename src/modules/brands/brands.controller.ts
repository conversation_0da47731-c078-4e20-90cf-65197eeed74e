import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { BrandsService } from './brands.service';
import { CreateBrandDto, UpdateBrandDto, BrandResponseDto } from './dto';
import { PaginationDto, PaginatedResult } from '@/common/dto/pagination.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { plainToClass } from 'class-transformer';

@ApiTags('Brands')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('brands')
export class BrandsController {
  constructor(private readonly brandsService: BrandsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new brand' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Brand created successfully',
    type: BrandResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Brand name already exists',
  })
  async create(
    @Body() createBrandDto: CreateBrandDto,
    @Request() req: any,
  ): Promise<BrandResponseDto> {
    const brand = await this.brandsService.create(createBrandDto, req.user?.username);
    return plainToClass(BrandResponseDto, brand, { excludeExtraneousValues: true });
  }

  @Get()
  @ApiOperation({ summary: 'Get all brands with pagination' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Brands retrieved successfully',
    type: [BrandResponseDto],
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResult<BrandResponseDto>> {
    const result = await this.brandsService.findAll(paginationDto);
    
    return {
      ...result,
      data: result.data.map(brand => 
        plainToClass(BrandResponseDto, brand, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a brand by ID' })
  @ApiParam({ name: 'id', description: 'Brand ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Brand retrieved successfully',
    type: BrandResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Brand not found',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<BrandResponseDto> {
    const brand = await this.brandsService.findOne(id);
    return plainToClass(BrandResponseDto, brand, { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a brand' })
  @ApiParam({ name: 'id', description: 'Brand ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Brand updated successfully',
    type: BrandResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Brand not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Brand name already exists',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateBrandDto: UpdateBrandDto,
    @Request() req: any,
  ): Promise<BrandResponseDto> {
    const brand = await this.brandsService.update(id, updateBrandDto, req.user?.username);
    return plainToClass(BrandResponseDto, brand, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a brand' })
  @ApiParam({ name: 'id', description: 'Brand ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Brand deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Brand not found',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.brandsService.remove(id, req.user?.username);
  }

  @Patch(':id/toggle-active')
  @ApiOperation({ summary: 'Toggle brand active status' })
  @ApiParam({ name: 'id', description: 'Brand ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Brand active status toggled successfully',
    type: BrandResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Brand not found',
  })
  async toggleActive(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<BrandResponseDto> {
    const brand = await this.brandsService.toggleActive(id, req.user?.username);
    return plainToClass(BrandResponseDto, brand, { excludeExtraneousValues: true });
  }
}
