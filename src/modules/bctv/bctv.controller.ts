import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { BctvService } from './bctv.service';
import { CreateBctvDto, UpdateBctvDto, BctvQueryDto } from './dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { RoleType } from '@/common/constants';
import { PermissionsType } from '@/common/constants';
import { User } from '@/entities';
import { QueueService } from '@/common/queue/queue.service';
import { JOB_TYPES } from '@/common/queue/queue.constants';
import { CustomLoggerService } from '@/common/logger/logger.service';

@ApiTags('BCTV Management')
@ApiBearerAuth('JWT-auth')
@Controller('bctv')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class BctvController {
  constructor(
    private readonly bctvService: BctvService,
    private readonly queueService: QueueService,
    private readonly logger: CustomLoggerService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new BCTV record' })
  @ApiResponse({ status: 201, description: 'BCTV record created successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  create(@Body() createBctvDto: CreateBctvDto, @CurrentUser() user: any) {
    return this.bctvService.create(createBctvDto, user.fullName || user.username);
  }

  @Get()
  @ApiOperation({ summary: 'Get all BCTV records with pagination and filtering' })
  @ApiResponse({ status: 200, description: 'BCTV records retrieved successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findAll(
    @Query() queryDto: BctvQueryDto
  ) {
    return this.bctvService.findAll(queryDto);
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get BCTV statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  getStatistics() {
    return this.bctvService.getStatistics();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get BCTV record by ID' })
  @ApiResponse({ status: 200, description: 'BCTV record retrieved successfully' })
  @ApiResponse({ status: 404, description: 'BCTV record not found' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findOne(@Param('id') id: string) {
    return this.bctvService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update BCTV record' })
  @ApiResponse({ status: 200, description: 'BCTV record updated successfully' })
  @ApiResponse({ status: 404, description: 'BCTV record not found' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  update(@Param('id') id: string, @Body() updateBctvDto: UpdateBctvDto) {
    return this.bctvService.update(id, updateBctvDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete BCTV record (soft delete)' })
  @ApiResponse({ status: 200, description: 'BCTV record deleted successfully' })
  @ApiResponse({ status: 404, description: 'BCTV record not found' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  remove(@Param('id') id: string, @CurrentUser() user: any) {
    return this.bctvService.remove(id, user.fullName || user.username);
  }

  // Import/Export Operations
  @Post('import')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Import BCTV data from Excel file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        skipFirstRow: {
          type: 'boolean',
          default: true,
        },
        sheetName: {
          type: 'string',
          description: 'Excel sheet name (optional)',
        },
      },
    },
  })
  @ApiResponse({ status: 202, description: 'Import job queued successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/temp',
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          cb(null, 'bctv-import-' + uniqueSuffix + extname(file.originalname));
        },
      }),
      fileFilter: (req, file, cb) => {
        const allowedMimeTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
          'application/vnd.ms-excel', // .xls
          'text/csv', // .csv
          'application/csv', // .csv alternative
          'text/plain' // .csv alternative
        ];
        
        const allowedExtensions = ['.xlsx', '.xls', '.csv'];
        const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
        
        if (allowedMimeTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
          cb(null, true);
        } else {
          cb(new Error(`Only Excel (.xlsx, .xls) and CSV files are allowed! Received: ${file.mimetype}, Extension: ${fileExtension}`), false);
        }
      },
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB
      },
    }),
  )
  async importBctvData(
    @UploadedFile() file: Express.Multer.File,
    @CurrentUser() currentUser: User,
    @Body('skipFirstRow') skipFirstRow = true,
    @Body('sheetName') sheetName?: string,
  ) {
    if (!file) {
      throw new Error('No file uploaded');
    }

    this.logger.log(
      `Starting BCTV import for user ${currentUser.id}, file: ${file.filename}`,
      'BctvController'
    );

    // Add job to queue
    const job = await this.queueService.addImportExportJob(JOB_TYPES.IMPORT_BCTV, {
      filePath: file.path,
      entityType: 'member-statistics',
      userId: currentUser.id,
      options: {
        skipFirstRow,
        sheetName: sheetName && sheetName !== 'string' && sheetName.trim() !== '' ? sheetName : undefined,
      },
    });

    return {
      jobId: job.id,
      entityType: 'member-statistics',
      status: 'queued',
      message: 'BCTV import job has been queued successfully',
    };
  }

  @Post('export')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Export BCTV data to Excel file' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        format: {
          type: 'string',
          enum: ['xlsx'],
          default: 'xlsx',
        },
        filters: {
          type: 'object',
          properties: {
            franchiseTree: { type: 'string' },
            member: { type: 'string' },
            memberLevel: { type: 'string' },
            isActive: { type: 'boolean' },
            minDepositAmount: { type: 'number' },
            maxDepositAmount: { type: 'number' },
          },
        },
        includeHeaders: {
          type: 'boolean',
          default: true,
        },
        sheetName: {
          type: 'string',
          default: 'BCTV Data',
        },
      },
    },
  })
  @ApiResponse({ status: 202, description: 'Export job queued successfully' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  async exportBctvData(
    @Body('format') format: 'xlsx' = 'xlsx',
    @Body('filters') filters: any = {},
    @Body('includeHeaders') includeHeaders: boolean = true,
    @Body('sheetName') sheetName: string = 'BCTV Data',
    @CurrentUser() currentUser: User,
  ) {
    this.logger.log(
      `Starting BCTV export for user ${currentUser.id}, format: ${format}`,
      'BctvController'
    );

    // Add job to queue
    const job = await this.queueService.addImportExportJob(JOB_TYPES.EXPORT_BCTV, {
      entityType: 'member-statistics',
      userId: currentUser.id,
      filters,
      format,
      options: {
        includeHeaders,
        sheetName,
      },
    });

    return {
      jobId: job.id,
      entityType: 'member-statistics',
      status: 'queued',
      format,
      message: 'BCTV export job has been queued successfully',
    };
  }
}
