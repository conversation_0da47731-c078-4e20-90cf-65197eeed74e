import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BctvService } from './bctv.service';
import { BctvController } from './bctv.controller';
// BctvData entity removed
import { QueueModule } from '@/common/queue/queue.module';

@Module({
  imports: [
    // BctvData entity removed
    QueueModule,
  ],
  controllers: [BctvController],
  providers: [BctvService],
  exports: [BctvService],
})
export class BctvModule {}
