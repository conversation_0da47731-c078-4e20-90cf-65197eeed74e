import { IsOptional, IsString, IsBoolean, IsN<PERSON>ber } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationDto } from '@/common';

export class BctvQueryDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo cây nhượng quyền',
    example: 'a0001'
  })
  @IsString()
  @IsOptional()
  franchiseTree?: string;

  @ApiPropertyOptional({
    description: 'Tìm kiếm theo phụ huynh nhượng quyền',
    example: 'a0000'
  })
  @IsString()
  @IsOptional()
  franchiseParent?: string;

  @ApiPropertyOptional({
    description: 'Tìm kiếm theo bên nhượng quyền',
    example: 'a0001'
  })
  @IsString()
  @IsOptional()
  franchiseSide?: string;

  @ApiPropertyOptional({
    description: 'Tìm kiếm theo thành viên',
    example: 'Ph0877'
  })
  @IsString()
  @IsOptional()
  member?: string;

  @ApiPropertyOptional({
    description: 'Tìm kiếm theo cấp thành viên',
    example: '一般层级'
  })
  @IsString()
  @IsOptional()
  memberLevel?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo mức độ',
    example: 1
  })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  level?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái hoạt động',
    example: true
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Số tiền gửi tối thiểu',
    example: 100
  })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  minDepositAmount?: number;

  @ApiPropertyOptional({
    description: 'Số tiền gửi tối đa',
    example: 10000
  })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  maxDepositAmount?: number;

  @ApiPropertyOptional({
    description: 'Số tiền cược tối thiểu',
    example: 50
  })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  minBetAmount?: number;

  @ApiPropertyOptional({
    description: 'Số tiền cược tối đa',
    example: 5000
  })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  maxBetAmount?: number;
}
