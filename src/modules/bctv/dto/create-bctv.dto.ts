import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>ber, IsBoolean, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateBctvDto {
  @ApiProperty({
    description: '<PERSON><PERSON>y nhượng quyền',
    example: 'a0001'
  })
  @IsString()
  @IsNotEmpty()
  franchiseTree: string;

  @ApiPropertyOptional({
    description: 'Phụ huynh nhượng quyền',
    example: 'a0000'
  })
  @IsString()
  @IsOptional()
  franchiseParent?: string;

  @ApiProperty({
    description: 'Bên nhượng quyền',
    example: 'a0001'
  })
  @IsString()
  @IsNotEmpty()
  franchiseSide: string;

  @ApiProperty({
    description: 'Mức độ',
    example: 1
  })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value) || 0)
  level: number;

  @ApiProperty({
    description: 'Thành viên',
    example: 'Ph0877'
  })
  @IsString()
  @IsNotEmpty()
  member: string;

  @ApiProperty({
    description: 'Cấp thành viên',
    example: '一般层级'
  })
  @IsString()
  @IsNotEmpty()
  memberLevel: string;

  @ApiPropertyOptional({
    description: 'Nhãn',
    example: 'VIP'
  })
  @IsString()
  @IsOptional()
  label?: string;

  @ApiPropertyOptional({
    description: 'Số lần gửi tiền',
    example: 5,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 0)
  depositCount?: number = 0;

  @ApiPropertyOptional({
    description: 'Tổng số tiền gửi',
    example: 1000.50,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  totalDepositAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Số lần rút tiền',
    example: 2,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 0)
  withdrawCount?: number = 0;

  @ApiPropertyOptional({
    description: 'Số tiền rút',
    example: 200.25,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  withdrawAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Số tiền cược',
    example: 10,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseInt(value) || 0)
  betCount?: number = 0;

  @ApiPropertyOptional({
    description: 'Tổng số tiền đặt cược',
    example: 500.00,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  totalBetAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Tổng số tiền đặt cược hợp lệ',
    example: 450.00,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  validBetAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Tổng số tiền chi trả',
    example: 300.00,
    default: 0
  })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  payoutAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Tổng tiền thưởng',
    example: 50.00,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  bonusAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Tổng khấu trừ',
    example: 25.00,
    default: 0
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  @Transform(({ value }) => parseFloat(value) || 0)
  deductionAmount?: number = 0;

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Thành viên VIP'
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động',
    example: true,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === true || value === 'true')
  isActive?: boolean = true;
}
