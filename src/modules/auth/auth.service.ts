import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';

import { User } from '@/entities/user.entity';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
// Affiliate services removed
import { CustomLoggerService } from '@/common/logger/logger.service';
import { AuditLogService } from '@/common/services/audit-log.service';
// AuditAction removed
import { RoleType } from '@/common/constants';


@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly logger: CustomLoggerService,
    private readonly auditLogService: AuditLogService,
  ) {}

  async validateUser(username: string, password: string): Promise<any> {
    this.logger.info(`Login attempt for user: ${username}`, 'AuthService');

    const user = await this.userRepository.findOne({
      where: [
        { username: username },
        { email: username }
      ],
      relations: [
        'role','teams', 'department',
        'department.teams',
        'menuAssignments', 'menuAssignments.permissions',
        'menuAssignments.menu', 'menuAssignments.submenu'
      ],
    });

    if (user && (await bcrypt.compare(password, user.password))) {
      this.logger.info(`Login successful for user: ${username}`, 'AuthService');
      const { password, ...result } = user;

      const permissions = new Set<string>();
      const menus = new Map<string, any>();

      user.menuAssignments?.forEach((menuAssignment) => {
        menuAssignment.permissions?.forEach((permission) => {
          permissions.add(permission.action);
        });

        // Collect menu information
        if (menuAssignment.menu && !menuAssignment.submenuId) {
          if (!menus.has(menuAssignment.menu.id)) {
            menus.set(menuAssignment.menu.id, {
              id: menuAssignment.menu.id,
              title: menuAssignment.menu.title,
              description: menuAssignment.menu.description,
              permissions: menuAssignment.permissions?.map(p => ({
                id: p.id,
                name: p.name,
                action: p.action,
                description: p.description
              })) || [],
              submenus: []
            });
          }
        }

        // Collect submenu information
        if (menuAssignment.submenu && menuAssignment.submenuId) {
          const submenuData = {
            id: menuAssignment.submenu.id,
            title: menuAssignment.submenu.title,
            description: menuAssignment.submenu.description,
            link: menuAssignment.submenu.link,
            permissions: menuAssignment.permissions?.map(p => ({
              id: p.id,
              name: p.name,
              action: p.action,
              description: p.description
            })) || []
          };

          // Ensure parent menu exists
          if (!menus.has(menuAssignment.menuId)) {
            // Load menu info if not already loaded
            menus.set(menuAssignment.menuId, {
              id: menuAssignment.menuId,
              title: menuAssignment.menu?.title || 'Unknown Menu',
              description: menuAssignment.menu?.description || null,
              permissions: [],
              submenus: []
            });
          }

          // Add submenu to parent menu
          const parentMenu = menus.get(menuAssignment.menuId);
          parentMenu.submenus.push(submenuData);
        }
      });

      const isSuperAdmin = ['super_admin'].includes(user.username);

      return {
        ...result,
        level: user.role?.level || null,
        department: user.department,
        teams: user.teams?.map(team => ({ id: team.id, name: team.name })) || [],
        departmentId: user.departmentId,
        roles: user.role ? [user.role.name] : isSuperAdmin ? [RoleType.SUPER_ADMIN] : [],
        permissions: Array.from(permissions),
        menus: Array.from(menus.values()),
        brandAccess: user.brandAccess || [],
      };
    }

    this.logger.warn(`Login failed for user: ${username}`, 'AuthService');
    return null;
  }

  async login(loginDto: LoginDto) {
    const { username, password } = loginDto;
    const validatedUser = await this.validateUser(username, password);

    if (!validatedUser) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Load user with full relations for menu/submenu information
    const user = await this.userRepository.findOne({
      where: { id: validatedUser.id },
      relations: [
        'role','teams', 'department',
        'department.teams',
        'menuAssignments', 'menuAssignments.permissions',
        'menuAssignments.menu', 'menuAssignments.submenu'
      ],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Get unique permissions from all menu assignments
    const permissions = new Set<string>();
    const menus = new Map<string, any>();

    user.menuAssignments?.forEach((menuAssignment) => {
      menuAssignment.permissions?.forEach((permission) => {
        permissions.add(permission.action);
      });

      // Collect menu information
      if (menuAssignment.menu && !menuAssignment.submenuId) {
        if (!menus.has(menuAssignment.menu.id)) {
          menus.set(menuAssignment.menu.id, {
            id: menuAssignment.menu.id,
            title: menuAssignment.menu.title,
            description: menuAssignment.menu.description,
            permissions: menuAssignment.permissions?.map(p => ({
              id: p.id,
              name: p.name,
              action: p.action,
              description: p.description
            })) || [],
            submenus: []
          });
        }
      }

      // Collect submenu information
      if (menuAssignment.submenu && menuAssignment.submenuId) {
        const submenuData = {
          id: menuAssignment.submenu.id,
          title: menuAssignment.submenu.title,
          description: menuAssignment.submenu.description,
          link: menuAssignment.submenu.link,
          permissions: menuAssignment.permissions?.map(p => ({
            id: p.id,
            name: p.name,
            action: p.action,
            description: p.description
          })) || []
        };

        // Ensure parent menu exists
        if (!menus.has(menuAssignment.menuId)) {
          // Load menu info if not already loaded
          menus.set(menuAssignment.menuId, {
            id: menuAssignment.menuId,
            title: menuAssignment.menu?.title || 'Unknown Menu',
            description: menuAssignment.menu?.description || null,
            permissions: [],
            submenus: []
          });
        }

        // Add submenu to parent menu
        const parentMenu = menus.get(menuAssignment.menuId);
        parentMenu.submenus.push(submenuData);
      }
    });

    const isSuperAdmin = ['super_admin'].includes(user.username);

    const payload = {
      id: user.id,
      email: user.email,
      username: user.username,
      level: user.role?.level || null,
      teams: user.teams?.map(team => ({ id: team.id, name: team.name })) || [],
      departmentId: user.departmentId,
      roles: user.role ? [user.role.name] : isSuperAdmin ? [RoleType.SUPER_ADMIN] : [],
      permissions: Array.from(permissions),
      whitelistIPs: user.whitelistIPs || [],
      brandAccess: user.brandAccess || [],
    };



    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        fullName: user.fullName,
        level: user.role?.level || null,
        roles: user.role ? [user.role.name] : isSuperAdmin ? [RoleType.SUPER_ADMIN] : [],
        permissions: Array.from(permissions),
        teams: user.teams?.map(team => ({ id: team.id, name: team.name })) || [],
        departmentId: user.departmentId,
        department: {
          id: user.department?.id,
          name: user.department?.name,
        },
        brandAccess: user.brandAccess || [],
        menus: Array.from(menus.values()),
      },
    };
  }

  async register(registerDto: RegisterDto) {
    const { email, password, username, fullName, phone, address, city, state, zip, country, referrerAffiliateCode } = registerDto;

    this.logger.info(`Registration attempt for user: ${username}`, 'AuthService', { email, username });

    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: [{ email }, { username }],
    });

    if (existingUser) {
      this.logger.warn(`Registration failed - user already exists: ${username}`, 'AuthService');
      throw new UnauthorizedException('User already exists');
    }

    try {
      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create new user
      const user = this.userRepository.create({
        email,
        username,
        fullName,
        password: hashedPassword,
        phone,
        address,
        city,
        state,
        zip,
        country,
      });

      const savedUser = await this.userRepository.save(user);

      // Process referral if affiliate code is provided
      if (referrerAffiliateCode) {
        // Affiliate functionality removed
        this.logger.info(`Affiliate functionality has been removed`, 'AuthService');
      }

      // Log registration audit
      await this.auditLogService.logAuthEvent(
        'CREATE',
        'User',
        savedUser.id,
        `User registered: ${savedUser.username}`
      );

      this.logger.info(`User registered successfully: ${username}`, 'AuthService', { userId: savedUser.id });

      const { password: _, ...result } = savedUser;
      return result;
    } catch (error) {
      this.logger.error(`Registration failed for user: ${username}`, error.stack, 'AuthService');
      throw error;
    }
  }

  async refreshToken(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: [
        'role', 'teams', 'department',
        'menuAssignments', 'menuAssignments.permissions',
        'menuAssignments.menu', 'menuAssignments.submenu'
      ],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Get unique permissions from all menu assignments
    const permissions = new Set<string>();
    const menus = new Map<string, any>();

    user.menuAssignments?.forEach((menuAssignment) => {
      menuAssignment.permissions?.forEach((permission) => {
        permissions.add(permission.action);
      });

      // Collect menu information
      if (menuAssignment.menu && !menuAssignment.submenuId) {
        if (!menus.has(menuAssignment.menu.id)) {
          menus.set(menuAssignment.menu.id, {
            id: menuAssignment.menu.id,
            title: menuAssignment.menu.title,
            description: menuAssignment.menu.description,
            permissions: menuAssignment.permissions?.map(p => ({
              id: p.id,
              name: p.name,
              action: p.action,
              description: p.description
            })) || [],
            submenus: []
          });
        }
      }

      // Collect submenu information
      if (menuAssignment.submenu && menuAssignment.submenuId) {
        const submenuData = {
          id: menuAssignment.submenu.id,
          title: menuAssignment.submenu.title,
          description: menuAssignment.submenu.description,
          link: menuAssignment.submenu.link,
          permissions: menuAssignment.permissions?.map(p => ({
            id: p.id,
            name: p.name,
            action: p.action,
            description: p.description
          })) || []
        };

        // Ensure parent menu exists
        if (!menus.has(menuAssignment.menuId)) {
          // Load menu info if not already loaded
          menus.set(menuAssignment.menuId, {
            id: menuAssignment.menuId,
            title: menuAssignment.menu?.title || 'Unknown Menu',
            description: menuAssignment.menu?.description || null,
            permissions: [],
            submenus: []
          });
        }

        // Add submenu to parent menu
        const parentMenu = menus.get(menuAssignment.menuId);
        parentMenu.submenus.push(submenuData);
      }
    });

    const isSuperAdmin = ['super_admin'].includes(user.username);

    const payload = {
      id: user.id,
      email: user.email,
      level: user.role?.level || null,
      teams: user.teams?.map(team => ({ id: team.id, name: team.name })) || [],
      departmentId: user.departmentId,
      roles: user.role ? [user.role.name] : isSuperAdmin ? [RoleType.SUPER_ADMIN] : [],
      permissions: Array.from(permissions),
      whitelistIPs: user.whitelistIPs || [],
      brandAccess: user.brandAccess || [],
    };

    return {
      access_token: this.jwtService.sign(payload),
    };
  }

  async getProfile(userId: string) {
    // Load user with full relations for menu/submenu information
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: [
        'role','team', 'department',
        'department.teams',
        'menuAssignments', 'menuAssignments.permissions',
        'menuAssignments.menu', 'menuAssignments.submenu'
      ],
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Get unique permissions from all menu assignments
    const permissions = new Set<string>();
    const menus = new Map<string, any>();

    user.menuAssignments?.forEach((menuAssignment) => {
      menuAssignment.permissions?.forEach((permission) => {
        permissions.add(permission.action);
      });

      // Collect menu information
      if (menuAssignment.menu && !menuAssignment.submenuId) {
        if (!menus.has(menuAssignment.menu.id)) {
          menus.set(menuAssignment.menu.id, {
            id: menuAssignment.menu.id,
            title: menuAssignment.menu.title,
            description: menuAssignment.menu.description,
            permissions: menuAssignment.permissions?.map(p => ({
              id: p.id,
              name: p.name,
              action: p.action,
              description: p.description
            })) || [],
            submenus: []
          });
        }
      }

      // Collect submenu information
      if (menuAssignment.submenu && menuAssignment.submenuId) {
        const submenuData = {
          id: menuAssignment.submenu.id,
          title: menuAssignment.submenu.title,
          description: menuAssignment.submenu.description,
          link: menuAssignment.submenu.link,
          permissions: menuAssignment.permissions?.map(p => ({
            id: p.id,
            name: p.name,
            action: p.action,
            description: p.description
          })) || []
        };

        // Ensure parent menu exists
        if (!menus.has(menuAssignment.menuId)) {
          // Load menu info if not already loaded
          menus.set(menuAssignment.menuId, {
            id: menuAssignment.menuId,
            title: menuAssignment.menu?.title || 'Unknown Menu',
            description: menuAssignment.menu?.description || null,
            permissions: [],
            submenus: []
          });
        }

        // Add submenu to parent menu
        const parentMenu = menus.get(menuAssignment.menuId);
        parentMenu.submenus.push(submenuData);
      }
    });

    const { password, menuAssignments, ...result } = user;

    const isSuperAdmin = ['super_admin'].includes(user.username);



    return {
      ...result,
      level: user.role?.level || null,
      teams: user.teams?.map(team => ({ id: team.id, name: team.name })) || [],
      departmentId: user.departmentId,
      roles: user.role ? [user.role.name] : isSuperAdmin ? [RoleType.SUPER_ADMIN] : [],
      permissions: Array.from(permissions),
      brandAccess: user.brandAccess || [],
      department: user.department? {
        id: user.department?.id,
        name: user.department?.name,
      } : undefined,
      menus: Array.from(menus.values()),
    };
  }
} 