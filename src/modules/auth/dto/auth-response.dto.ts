import { DepartmentSummaryDto, TeamSummaryDto } from '@/modules/users/dto/user-response.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
export class MenuPermissionDto {
  @ApiProperty({ description: 'Permission ID' })
  id: string;

  @ApiProperty({ description: 'Permission name' })
  name: string;

  @ApiProperty({ description: 'Permission action' })
  action: string;

  @ApiProperty({ description: 'Permission description' })
  description: string;
}

export class SubMenuDto {
  @ApiProperty({ description: 'SubMenu ID' })
  id: string;

  @ApiProperty({ description: 'SubMenu title' })
  title: string;

  @ApiProperty({ description: 'SubMenu description' })
  description: string;

  @ApiProperty({ description: 'SubMenu link' })
  link: string;

  @ApiProperty({
    description: 'SubMenu permissions',
    type: [MenuPermissionDto]
  })
  permissions: MenuPermissionDto[];
}

export class MenuDto {
  @ApiProperty({ description: 'Menu ID' })
  id: string;

  @ApiProperty({ description: 'Menu title' })
  title: string;

  @ApiProperty({ description: 'Menu description' })
  description: string;

  @ApiProperty({
    description: 'Menu permissions',
    type: [MenuPermissionDto]
  })
  permissions: MenuPermissionDto[];

  @ApiProperty({
    description: 'Submenus under this menu',
    type: [SubMenuDto]
  })
  submenus: SubMenuDto[];
}

export class UserProfileDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'Username' })
  username: string;

  @ApiPropertyOptional({ description: 'Email', nullable: true })
  email?: string;

  @ApiProperty({ description: 'Full name' })
  fullName: string;

  @ApiProperty({ description: 'User level', nullable: true })
  level: number | null;

  @ApiProperty({ description: 'User roles', type: [String] })
  roles: string[];

  @ApiProperty({ description: 'User permissions', type: [String] })
  permissions: string[];

  @ApiPropertyOptional({ description: 'User team ID', nullable: true })
  @Expose()
  teamId?: string;

  @ApiPropertyOptional({ description: 'User department', type: DepartmentSummaryDto })
  @Expose()
  department?: {
    id: string;
    name: string;
  };
  
  @ApiPropertyOptional({ description: 'User department ID', nullable: true })
  @Expose()
  departmentId?: string;

  @ApiPropertyOptional({ description: 'User teams', type: [TeamSummaryDto] })
  @Expose()
  teams?: TeamSummaryDto[];

  @ApiProperty({
    description: 'User accessible menus with nested submenus',
    type: [MenuDto]
  })
  menus: MenuDto[];
}

export class LoginResponseDto {
  @ApiProperty({ description: 'JWT access token' })
  access_token: string;

  @ApiProperty({ 
    description: 'User profile information',
    type: UserProfileDto
  })
  user: UserProfileDto;
}
