import { Injectable } from '@nestjs/common';
import { QueueService } from '@/common/queue/queue.service';
import { QUEUE_NAMES } from '@/common/queue/queue.constants';
import { CustomLoggerService } from '@/common/logger/logger.service';

export interface QueueHealthStatus {
  queueName: string;
  isHealthy: boolean;
  status: 'healthy' | 'warning' | 'critical';
  metrics: {
    totalJobs: number;
    waitingJobs: number;
    activeJobs: number;
    completedJobs: number;
    failedJobs: number;
    delayedJobs: number;
    pausedJobs: number;
  };
  alerts: string[];
  lastChecked: Date;
}

export interface SystemHealthStatus {
  overall: 'healthy' | 'warning' | 'critical';
  queues: QueueHealthStatus[];
  summary: {
    totalQueues: number;
    healthyQueues: number;
    warningQueues: number;
    criticalQueues: number;
    totalJobs: number;
    totalFailedJobs: number;
  };
  lastChecked: Date;
}

@Injectable()
export class QueueMonitorService {
  constructor(
    private readonly queueService: QueueService,
    private readonly logger: CustomLoggerService,
  ) {}

  /**
   * Get health status for all queues
   */
  async getSystemHealth(): Promise<SystemHealthStatus> {
    try {
      const queueNames = Object.values(QUEUE_NAMES);
      const queueHealthStatuses = await Promise.all(
        queueNames.map(queueName => this.getQueueHealth(queueName))
      );

      const healthyQueues = queueHealthStatuses.filter(q => q.status === 'healthy').length;
      const warningQueues = queueHealthStatuses.filter(q => q.status === 'warning').length;
      const criticalQueues = queueHealthStatuses.filter(q => q.status === 'critical').length;

      const totalJobs = queueHealthStatuses.reduce((sum, q) => sum + q.metrics.totalJobs, 0);
      const totalFailedJobs = queueHealthStatuses.reduce((sum, q) => sum + q.metrics.failedJobs, 0);

      let overallStatus: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (criticalQueues > 0) {
        overallStatus = 'critical';
      } else if (warningQueues > 0) {
        overallStatus = 'warning';
      }

      return {
        overall: overallStatus,
        queues: queueHealthStatuses,
        summary: {
          totalQueues: queueNames.length,
          healthyQueues,
          warningQueues,
          criticalQueues,
          totalJobs,
          totalFailedJobs,
        },
        lastChecked: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get system health', error.stack, 'QueueMonitorService');
      throw error;
    }
  }

  /**
   * Get health status for a specific queue
   */
  async getQueueHealth(queueName: string): Promise<QueueHealthStatus> {
    try {
      const stats = await this.queueService.getQueueStats(queueName);
      const alerts: string[] = [];
      
      const totalJobs = Object.values(stats.counts).reduce((sum, count) => sum + count, 0);
      const failedJobs = stats.counts.failed;
      const waitingJobs = stats.counts.waiting;
      const activeJobs = stats.counts.active;

      // Health check rules
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // Critical conditions
      if (failedJobs > 50) {
        status = 'critical';
        alerts.push(`High number of failed jobs: ${failedJobs}`);
      }
      if (waitingJobs > 100) {
        status = 'critical';
        alerts.push(`High number of waiting jobs: ${waitingJobs}`);
      }
      if (activeJobs > 20) {
        status = 'critical';
        alerts.push(`High number of active jobs: ${activeJobs}`);
      }

      // Warning conditions
      if (status === 'healthy') {
        if (failedJobs > 10) {
          status = 'warning';
          alerts.push(`Moderate number of failed jobs: ${failedJobs}`);
        }
        if (waitingJobs > 50) {
          status = 'warning';
          alerts.push(`Moderate number of waiting jobs: ${waitingJobs}`);
        }
        if (activeJobs > 10) {
          status = 'warning';
          alerts.push(`Moderate number of active jobs: ${activeJobs}`);
        }
      }

      // Calculate failure rate
      if (totalJobs > 0) {
        const failureRate = (failedJobs / totalJobs) * 100;
        if (failureRate > 20) {
          status = 'critical';
          alerts.push(`High failure rate: ${failureRate.toFixed(2)}%`);
        } else if (failureRate > 10) {
          if (status === 'healthy') status = 'warning';
          alerts.push(`Moderate failure rate: ${failureRate.toFixed(2)}%`);
        }
      }

      return {
        queueName,
        isHealthy: status === 'healthy',
        status,
        metrics: {
          totalJobs,
          waitingJobs: stats.counts.waiting,
          activeJobs: stats.counts.active,
          completedJobs: stats.counts.completed,
          failedJobs: stats.counts.failed,
          delayedJobs: stats.counts.delayed,
          pausedJobs: stats.counts.paused,
        },
        alerts,
        lastChecked: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get health for queue ${queueName}`, error.stack, 'QueueMonitorService');
      
      return {
        queueName,
        isHealthy: false,
        status: 'critical',
        metrics: {
          totalJobs: 0,
          waitingJobs: 0,
          activeJobs: 0,
          completedJobs: 0,
          failedJobs: 0,
          delayedJobs: 0,
          pausedJobs: 0,
        },
        alerts: [`Failed to check queue health: ${error.message}`],
        lastChecked: new Date(),
      };
    }
  }

  /**
   * Get queue performance metrics
   */
  async getQueuePerformanceMetrics(queueName: string, timeRange: '1h' | '24h' | '7d' = '24h') {
    try {
      // This is a placeholder for performance metrics
      // In a real implementation, you would:
      // 1. Store job completion times in Redis or database
      // 2. Calculate average processing time, throughput, etc.
      // 3. Return historical performance data

      const stats = await this.queueService.getQueueStats(queueName);
      
      return {
        queueName,
        timeRange,
        metrics: {
          averageProcessingTime: 0, // Would calculate from historical data
          throughput: 0, // Jobs per hour
          successRate: 0, // Percentage of successful jobs
          errorRate: 0, // Percentage of failed jobs
          peakHours: [], // Hours with highest activity
        },
        currentStats: stats.counts,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get performance metrics for queue ${queueName}`, error.stack, 'QueueMonitorService');
      throw error;
    }
  }

  /**
   * Get failed jobs with details
   */
  async getFailedJobsDetails(queueName: string, limit: number = 50) {
    try {
      const stats = await this.queueService.getQueueStats(queueName);
      
      return {
        queueName,
        failedJobs: stats.jobs.failed.slice(0, limit).map(job => ({
          id: job.id,
          name: job.name,
          data: job.data,
          failedReason: job.failedReason,
          stacktrace: job.stacktrace,
          attemptsMade: job.attemptsMade,
          timestamp: job.timestamp,
          finishedOn: job.finishedOn,
        })),
        totalFailedJobs: stats.counts.failed,
        limit,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get failed jobs for queue ${queueName}`, error.stack, 'QueueMonitorService');
      throw error;
    }
  }

  /**
   * Get queue trends (placeholder for future implementation)
   */
  async getQueueTrends(queueName: string, period: '1h' | '24h' | '7d' = '24h') {
    try {
      // This would typically query historical data from a time-series database
      // For now, return current stats as a placeholder
      
      const stats = await this.queueService.getQueueStats(queueName);
      
      return {
        queueName,
        period,
        trends: {
          jobVolume: [], // Array of { timestamp, count } objects
          processingTime: [], // Array of { timestamp, avgTime } objects
          errorRate: [], // Array of { timestamp, errorRate } objects
        },
        currentSnapshot: stats.counts,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get trends for queue ${queueName}`, error.stack, 'QueueMonitorService');
      throw error;
    }
  }

  /**
   * Generate queue health report
   */
  async generateHealthReport() {
    try {
      const systemHealth = await this.getSystemHealth();
      
      const report = {
        reportId: `health-report-${Date.now()}`,
        generatedAt: new Date(),
        systemHealth,
        recommendations: this.generateRecommendations(systemHealth),
        summary: {
          overallStatus: systemHealth.overall,
          criticalIssues: systemHealth.queues.filter(q => q.status === 'critical').length,
          warningIssues: systemHealth.queues.filter(q => q.status === 'warning').length,
          totalAlerts: systemHealth.queues.reduce((sum, q) => sum + q.alerts.length, 0),
        },
      };

      this.logger.info('Queue health report generated', 'QueueMonitorService', {
        reportId: report.reportId,
        overallStatus: report.systemHealth.overall,
        criticalIssues: report.summary.criticalIssues,
      });

      return report;
    } catch (error) {
      this.logger.error('Failed to generate health report', error.stack, 'QueueMonitorService');
      throw error;
    }
  }

  /**
   * Generate recommendations based on health status
   */
  private generateRecommendations(systemHealth: SystemHealthStatus): string[] {
    const recommendations: string[] = [];

    systemHealth.queues.forEach(queue => {
      if (queue.status === 'critical') {
        if (queue.metrics.failedJobs > 50) {
          recommendations.push(`Queue ${queue.queueName}: Consider investigating and retrying failed jobs`);
        }
        if (queue.metrics.waitingJobs > 100) {
          recommendations.push(`Queue ${queue.queueName}: Consider scaling up workers to handle backlog`);
        }
        if (queue.metrics.activeJobs > 20) {
          recommendations.push(`Queue ${queue.queueName}: Monitor for stuck jobs or increase timeout settings`);
        }
      }
    });

    if (systemHealth.summary.totalFailedJobs > 100) {
      recommendations.push('System-wide: High number of failed jobs detected. Review error patterns and job configurations');
    }

    if (recommendations.length === 0) {
      recommendations.push('All queues are operating within normal parameters');
    }

    return recommendations;
  }
}
