import { Module } from '@nestjs/common';
import { QueueModule } from '@/common/queue';
import { QueueMonitorController } from './queue-monitor.controller';
import { QueueMonitorService } from './queue-monitor.service';

@Module({
  imports: [QueueModule],
  controllers: [QueueMonitorController],
  providers: [QueueMonitorService],
  exports: [QueueMonitorService],
})
export class QueueMonitorModule {}
