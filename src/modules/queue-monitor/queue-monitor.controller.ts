import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { QueueMonitorService, SystemHealthStatus, QueueHealthStatus } from './queue-monitor.service';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { RoleType } from '@/common/constants/role.constant';
import { PermissionsType } from '@/common/constants/permissions.constant';

@ApiTags('Queue Monitor')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@Controller('queue-monitor')
export class QueueMonitorController {
  constructor(private readonly queueMonitorService: QueueMonitorService) {}

  @Get('health')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy trạng thái sức khỏe tổng thể của hệ thống queue',
    description: 'Trả về thông tin chi tiết về trạng thái sức khỏe của tất cả các queue trong hệ thống',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trạng thái sức khỏe hệ thống queue',
    schema: {
      type: 'object',
      properties: {
        overall: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
        queues: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              queueName: { type: 'string' },
              isHealthy: { type: 'boolean' },
              status: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
              metrics: {
                type: 'object',
                properties: {
                  totalJobs: { type: 'number' },
                  waitingJobs: { type: 'number' },
                  activeJobs: { type: 'number' },
                  completedJobs: { type: 'number' },
                  failedJobs: { type: 'number' },
                  delayedJobs: { type: 'number' },
                  pausedJobs: { type: 'number' },
                },
              },
              alerts: { type: 'array', items: { type: 'string' } },
              lastChecked: { type: 'string', format: 'date-time' },
            },
          },
        },
        summary: {
          type: 'object',
          properties: {
            totalQueues: { type: 'number' },
            healthyQueues: { type: 'number' },
            warningQueues: { type: 'number' },
            criticalQueues: { type: 'number' },
            totalJobs: { type: 'number' },
            totalFailedJobs: { type: 'number' },
          },
        },
        lastChecked: { type: 'string', format: 'date-time' },
      },
    },
  })
  async getSystemHealth(): Promise<SystemHealthStatus> {
    return await this.queueMonitorService.getSystemHealth();
  }

  @Get('health/:queueName')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy trạng thái sức khỏe của queue cụ thể',
    description: 'Trả về thông tin chi tiết về trạng thái sức khỏe của một queue cụ thể',
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trạng thái sức khỏe queue',
  })
  async getQueueHealth(@Param('queueName') queueName: string): Promise<QueueHealthStatus> {
    return await this.queueMonitorService.getQueueHealth(queueName);
  }

  @Get('performance/:queueName')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy metrics hiệu suất của queue',
    description: 'Trả về các chỉ số hiệu suất như thời gian xử lý trung bình, throughput, success rate',
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiQuery({ 
    name: 'timeRange', 
    required: false, 
    description: 'Khoảng thời gian', 
    enum: ['1h', '24h', '7d'],
    example: '24h'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Metrics hiệu suất queue',
    schema: {
      type: 'object',
      properties: {
        queueName: { type: 'string' },
        timeRange: { type: 'string' },
        metrics: {
          type: 'object',
          properties: {
            averageProcessingTime: { type: 'number' },
            throughput: { type: 'number' },
            successRate: { type: 'number' },
            errorRate: { type: 'number' },
            peakHours: { type: 'array', items: { type: 'number' } },
          },
        },
        currentStats: { type: 'object' },
        generatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  async getQueuePerformance(
    @Param('queueName') queueName: string,
    @Query('timeRange') timeRange: '1h' | '24h' | '7d' = '24h',
  ) {
    return await this.queueMonitorService.getQueuePerformanceMetrics(queueName, timeRange);
  }

  @Get('failed-jobs/:queueName')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy danh sách jobs thất bại',
    description: 'Trả về danh sách các jobs thất bại với thông tin chi tiết về lỗi',
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiQuery({ 
    name: 'limit', 
    required: false, 
    description: 'Số lượng jobs tối đa', 
    example: 50,
    type: 'number'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách jobs thất bại',
    schema: {
      type: 'object',
      properties: {
        queueName: { type: 'string' },
        failedJobs: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              data: { type: 'object' },
              failedReason: { type: 'string' },
              stacktrace: { type: 'array', items: { type: 'string' } },
              attemptsMade: { type: 'number' },
              timestamp: { type: 'number' },
              finishedOn: { type: 'number' },
            },
          },
        },
        totalFailedJobs: { type: 'number' },
        limit: { type: 'number' },
        generatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  async getFailedJobs(
    @Param('queueName') queueName: string,
    @Query('limit') limit: number = 50,
  ) {
    return await this.queueMonitorService.getFailedJobsDetails(queueName, limit);
  }

  @Get('trends/:queueName')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy xu hướng hoạt động của queue',
    description: 'Trả về dữ liệu xu hướng về volume, thời gian xử lý và tỷ lệ lỗi theo thời gian',
  })
  @ApiParam({ name: 'queueName', description: 'Tên queue', example: 'import-export' })
  @ApiQuery({ 
    name: 'period', 
    required: false, 
    description: 'Khoảng thời gian', 
    enum: ['1h', '24h', '7d'],
    example: '24h'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xu hướng hoạt động queue',
    schema: {
      type: 'object',
      properties: {
        queueName: { type: 'string' },
        period: { type: 'string' },
        trends: {
          type: 'object',
          properties: {
            jobVolume: { type: 'array' },
            processingTime: { type: 'array' },
            errorRate: { type: 'array' },
          },
        },
        currentSnapshot: { type: 'object' },
        generatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  async getQueueTrends(
    @Param('queueName') queueName: string,
    @Query('period') period: '1h' | '24h' | '7d' = '24h',
  ) {
    return await this.queueMonitorService.getQueueTrends(queueName, period);
  }

  @Get('report/health')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Tạo báo cáo sức khỏe hệ thống',
    description: 'Tạo báo cáo tổng hợp về trạng thái sức khỏe của toàn bộ hệ thống queue với khuyến nghị',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Báo cáo sức khỏe hệ thống',
    schema: {
      type: 'object',
      properties: {
        reportId: { type: 'string' },
        generatedAt: { type: 'string', format: 'date-time' },
        systemHealth: { type: 'object' },
        recommendations: { type: 'array', items: { type: 'string' } },
        summary: {
          type: 'object',
          properties: {
            overallStatus: { type: 'string' },
            criticalIssues: { type: 'number' },
            warningIssues: { type: 'number' },
            totalAlerts: { type: 'number' },
          },
        },
      },
    },
  })
  async generateHealthReport() {
    return await this.queueMonitorService.generateHealthReport();
  }

  @Get('dashboard')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({
    summary: 'Lấy dữ liệu dashboard tổng quan',
    description: 'Trả về tất cả dữ liệu cần thiết cho dashboard monitoring queue',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dữ liệu dashboard queue monitoring',
    schema: {
      type: 'object',
      properties: {
        systemHealth: { type: 'object' },
        recentFailedJobs: { type: 'array' },
        queuePerformance: { type: 'object' },
        alerts: { type: 'array', items: { type: 'string' } },
        lastUpdated: { type: 'string', format: 'date-time' },
      },
    },
  })
  async getDashboardData() {
    const systemHealth = await this.queueMonitorService.getSystemHealth();
    
    // Get recent failed jobs from all queues
    const recentFailedJobs: any[] = [];
    for (const queue of systemHealth.queues) {
      if (queue.metrics.failedJobs > 0) {
        const failedJobs = await this.queueMonitorService.getFailedJobsDetails(queue.queueName, 5);
        recentFailedJobs.push(...failedJobs.failedJobs.map(job => ({
          ...job,
          queueName: queue.queueName,
        })));
      }
    }

    // Sort by timestamp and take most recent
    recentFailedJobs.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
    const topRecentFailedJobs = recentFailedJobs.slice(0, 20);

    // Collect all alerts
    const alerts = systemHealth.queues.reduce((acc, queue) => {
      return acc.concat(queue.alerts.map(alert => `${queue.queueName}: ${alert}`));
    }, [] as string[]);

    return {
      systemHealth,
      recentFailedJobs: topRecentFailedJobs,
      alerts,
      lastUpdated: new Date(),
    };
  }
}
