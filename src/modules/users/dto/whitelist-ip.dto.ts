import { IsArray, IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateWhitelistIPDto {
  @ApiProperty({
    description: 'List of whitelisted IP addresses',
    example: ['***********', '********', '***********/24'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  whitelistIPs: string[];
}

export class AddWhitelistIPDto {
  @ApiProperty({
    description: 'IP address to add to whitelist',
    example: '*************',
  })
  @IsString()
  ipAddress: string;
}

export class RemoveWhitelistIPDto {
  @ApiProperty({
    description: 'IP address to remove from whitelist',
    example: '*************',
  })
  @IsString()
  ipAddress: string;
}
