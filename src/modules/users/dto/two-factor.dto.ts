import { IsString, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class EnableTwoFactorDto {
  @ApiProperty({
    description: 'Two-factor authentication secret',
    example: 'JBSWY3DPEHPK3PXP',
  })
  @IsString()
  secret: string;
}

export class VerifyTwoFactorDto {
  @ApiProperty({
    description: 'Two-factor authentication token',
    example: '123456',
  })
  @IsString()
  token: string;
}

export class UpdateTwoFactorDto {
  @ApiPropertyOptional({
    description: 'Enable or disable two-factor authentication',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  twoFactorEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Two-factor authentication secret (only when enabling)',
    example: 'JBSWY3DPEHPK3PXP',
  })
  @IsOptional()
  @IsString()
  twoFactorSecret?: string;
}
