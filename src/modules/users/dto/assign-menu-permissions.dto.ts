import { IsArray, IsOptional, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class MenuPermissionDto {
  @ApiProperty({
    description: 'Menu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Array of permission IDs for this menu',
    type: [String],
    example: ['perm1-uuid', 'perm2-uuid'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds: string[];
}

export class SubMenuPermissionDto {
  @ApiProperty({
    description: 'SubMenu ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Array of permission IDs for this submenu',
    type: [String],
    example: ['perm1-uuid', 'perm2-uuid'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds: string[];
}

export class AssignMenuPermissionsDto {
  @ApiPropertyOptional({
    description: 'Menu assignments with permissions',
    type: [MenuPermissionDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MenuPermissionDto)
  menu?: MenuPermissionDto[];

  @ApiPropertyOptional({
    description: 'SubMenu assignments with permissions',
    type: [SubMenuPermissionDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SubMenuPermissionDto)
  subMenu?: SubMenuPermissionDto[];
}
