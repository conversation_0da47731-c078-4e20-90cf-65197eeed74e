import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { PaginationDto } from "@/common/dto/pagination.dto";

export class UserFilterDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Team ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  teamId?: string;

  @ApiPropertyOptional({
    description: 'Department ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  roleId?: string;
}