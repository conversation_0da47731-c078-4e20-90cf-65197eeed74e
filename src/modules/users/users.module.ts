import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@/entities/user.entity';
import { Role } from '@/entities/role.entity';
import { Team } from '@/entities/team.entity';
import { Department } from '@/entities/department.entity';
import { UserMenuAssignment } from '@/entities/user-menu-assignment.entity';
import { Menu } from '@/entities/menu.entity';
import { SubMenu } from '@/entities/submenu.entity';
import { Permission } from '@/entities/permission.entity';
import { UserRoleAssignment } from '@/entities/user-role-assignment.entity';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';

@Module({
  imports: [TypeOrmModule.forFeature([
    User,
    Role,
    Team,
    Department,
    UserMenuAssignment,
    Menu,
    SubMenu,
    Permission,
    UserRoleAssignment,
  ])],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
