import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { ProposalsService } from './proposals.service';
import { CreateProposalDto, UpdateProposalDto, ProposalQueryDto, ProposalResponseDto } from './dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { RoleType, PermissionsType } from '@/common/constants';

@ApiTags('Proposals')
@Controller('proposals')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class ProposalsController {
  constructor(private readonly proposalsService: ProposalsService) {}

  @Post()
  @ApiOperation({ summary: 'Create new proposal' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Proposal created successfully',
    type: ProposalResponseDto,
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.CREATE)
  async create(
    @Body() createProposalDto: CreateProposalDto,
    @CurrentUser() currentUser: any,
  ) {
    const proposal = await this.proposalsService.create(
      createProposalDto,
      currentUser.fullName || currentUser.username
    );
    return plainToClass(ProposalResponseDto, proposal, { excludeExtraneousValues: true });
  }

  @Get()
  @ApiOperation({ summary: 'Get all proposals with pagination and filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Proposals retrieved successfully',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async findAll(@Query() queryDto: ProposalQueryDto) {
    const result = await this.proposalsService.findAll(queryDto);
    return {
      ...result,
      data: result.data.map(proposal => 
        plainToClass(ProposalResponseDto, proposal, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get proposal statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Statistics retrieved successfully',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  async getStatistics() {
    return this.proposalsService.getStatistics();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get proposal by ID' })
  @ApiParam({ name: 'id', description: 'Proposal ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Proposal retrieved successfully',
    type: ProposalResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Proposal not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    const proposal = await this.proposalsService.findOne(id);
    return plainToClass(ProposalResponseDto, proposal, { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update proposal' })
  @ApiParam({ name: 'id', description: 'Proposal ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Proposal updated successfully',
    type: ProposalResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Proposal not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateProposalDto: UpdateProposalDto,
    @CurrentUser() currentUser: any,
  ) {
    const proposal = await this.proposalsService.update(
      id,
      updateProposalDto,
      currentUser.fullName || currentUser.username
    );
    return plainToClass(ProposalResponseDto, proposal, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete proposal (soft delete)' })
  @ApiParam({ name: 'id', description: 'Proposal ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Proposal deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Proposal not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: any,
  ) {
    const success = await this.proposalsService.remove(
      id,
      currentUser.fullName || currentUser.username
    );
    return { success, message: 'Proposal deleted successfully' };
  }

  @Post(':id/approve')
  @ApiOperation({ summary: 'Approve proposal' })
  @ApiParam({ name: 'id', description: 'Proposal ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Proposal approved successfully',
    type: ProposalResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Proposal not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Proposal cannot be approved',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  async approve(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: any,
  ) {
    const proposal = await this.proposalsService.approve(id, currentUser.id);
    return plainToClass(ProposalResponseDto, proposal, { excludeExtraneousValues: true });
  }

  @Post(':id/reject')
  @ApiOperation({ summary: 'Reject proposal' })
  @ApiParam({ name: 'id', description: 'Proposal ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Proposal rejected successfully',
    type: ProposalResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Proposal not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Proposal cannot be rejected',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  async reject(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('rejectionReason') rejectionReason: string,
    @CurrentUser() currentUser: any,
  ) {
    const proposal = await this.proposalsService.reject(id, currentUser.id, rejectionReason);
    return plainToClass(ProposalResponseDto, proposal, { excludeExtraneousValues: true });
  }
}
