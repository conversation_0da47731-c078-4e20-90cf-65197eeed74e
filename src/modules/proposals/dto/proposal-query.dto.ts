import { IsOptional, IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsDate } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { ProposalStatus } from '@/entities/invoice-proposals.entity';

export class ProposalQueryDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên tài nguyên',
    example: 'máy chủ',
  })
  @IsOptional()
  @IsString()
  resourceName?: string;

  @ApiPropertyOptional({
    description: 'Tìm kiếm theo hậu đài',
    example: 'Công ty ABC',
  })
  @IsOptional()
  @IsString()
  sponsor?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái',
    enum: ProposalStatus,
  })
  @IsOptional()
  @IsEnum(ProposalStatus)
  status?: ProposalStatus;

  @ApiPropertyOptional({
    description: 'Lọc theo ID người đề xuất',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  proposerId?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo ID bộ phận',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo danh mục',
    example: 'IT Infrastructure',
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo mức độ ưu tiên',
    example: 1,
    minimum: 1,
    maximum: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  priority?: number;

  @ApiPropertyOptional({
    description: 'Ngày đề xuất từ',
    example: '2025-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  proposalDateFrom?: Date;

  @ApiPropertyOptional({
    description: 'Ngày đề xuất đến',
    example: '2025-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  proposalDateTo?: Date;

  @ApiPropertyOptional({
    description: 'Tổng tiền từ',
    example: 1000000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalAmountFrom?: number;

  @ApiPropertyOptional({
    description: 'Tổng tiền đến',
    example: 100000000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalAmountTo?: number;

  @ApiPropertyOptional({
    description: 'Chỉ hiển thị đề xuất quá hạn',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  overdue?: boolean;
}
