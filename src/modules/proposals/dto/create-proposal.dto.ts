import { IsString, IsO<PERSON>al, <PERSON><PERSON><PERSON>ber, IsEnum, IsArray, <PERSON><PERSON><PERSON>D, <PERSON>, <PERSON>, IsDate } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProposalStatus } from '@/entities/invoice-proposals.entity';
import { Type } from 'class-transformer';

export class CreateProposalDto {
  @ApiProperty({
    description: '<PERSON><PERSON>u đài (người giới thiệu)',
    example: 'Công ty ABC',
  })
  @IsString()
  sponsor: string;

  @ApiProperty({
    description: 'ID người đề xuất',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  proposerId: string;

  @ApiProperty({
    description: 'Tên tài nguyên',
    example: 'M<PERSON>y chủ web mới',
  })
  @IsString()
  resourceName: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON> tả chi tiết',
    example: '<PERSON><PERSON><PERSON> chủ web để nâng cấp hệ thống hiện tại',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Cần triển khai trong Q1 2025',
  })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    description: 'Đơn giá',
    example: 50000000,
  })
  @IsNumber()
  @Min(0)
  unitPrice: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 2,
    default: 1,
  })
  @IsNumber()
  @Min(1)
  quantity: number = 1;

  @ApiProperty({
    description: 'Phí',
    example: 5000000,
    default: 0,
  })
  @IsNumber()
  @Min(0)
  fee: number = 0;

  @ApiProperty({
    description: 'Tổng tiền',
    example: 105000000,
  })
  @IsNumber()
  @Min(0)
  totalAmount: number;

  @ApiPropertyOptional({
    description: 'Thông tin đối tác',
    example: 'Công ty TNHH Công nghệ XYZ',
  })
  @IsOptional()
  @IsString()
  partnerInfo?: string;

  @ApiPropertyOptional({
    description: 'Mục đích sử dụng',
    example: 'Nâng cấp hạ tầng IT',
  })
  @IsOptional()
  @IsString()
  purpose?: string;

  @ApiPropertyOptional({
    description: 'Thông tin thanh toán (JSON format)',
    example: { method: 'bank_transfer', account: '*********', bank: 'Vietcombank' },
  })
  @IsOptional()
  chargeInfo?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Trạng thái đề xuất',
    enum: ProposalStatus,
    default: ProposalStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(ProposalStatus)
  status?: ProposalStatus = ProposalStatus.PENDING;

  @ApiProperty({
    description: 'Ngày đề xuất',
    example: '2025-01-15T10:30:00Z',
  })
  @IsDate()
  @Type(() => Date)
  proposalDate: Date;

  @ApiPropertyOptional({
    description: 'ID bộ phận liên quan',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Mức độ ưu tiên (1 = cao nhất, 5 = thấp nhất)',
    example: 2,
    minimum: 1,
    maximum: 5,
    default: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  priority?: number = 1;

  @ApiPropertyOptional({
    description: 'Danh mục đề xuất',
    example: 'IT Infrastructure',
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({
    description: 'File đính kèm (URLs)',
    example: ['https://example.com/file1.pdf', 'https://example.com/file2.docx'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];

  @ApiPropertyOptional({
    description: 'Ngân sách dự kiến',
    example: 100000000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  estimatedBudget?: number;

  @ApiPropertyOptional({
    description: 'Ngày dự kiến hoàn thành',
    example: '2025-03-31T23:59:59Z',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  expectedCompletionDate?: Date;
}
