import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';
import { ProposalStatus } from '@/entities/invoice-proposals.entity';

export class ProposerSummaryDto {
  @Expose()
  id: string;

  @Expose()
  username: string;

  @Expose()
  fullName: string;
}

export class ReviewerSummaryDto {
  @Expose()
  id: string;

  @Expose()
  username: string;

  @Expose()
  fullName: string;
}

export class DepartmentSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

export class ProposalResponseDto {
  @ApiProperty({
    description: 'Proposal ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Hậu đài (người giới thiệu)',
    example: 'Công ty ABC',
  })
  @Expose()
  sponsor: string;

  @ApiProperty({
    description: 'Tên tài nguyên',
    example: '<PERSON><PERSON><PERSON> chủ web mới',
  })
  @Expose()
  resourceName: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON> tả chi tiết',
    example: 'Máy chủ web để nâng cấp hệ thống hiện tại',
  })
  @Expose()
  description?: string;

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Cần triển khai trong Q1 2025',
  })
  @Expose()
  note?: string;

  @ApiProperty({
    description: 'Đơn giá',
    example: 50000000,
  })
  @Expose()
  unitPrice: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 2,
  })
  @Expose()
  quantity: number;

  @ApiProperty({
    description: 'Phí',
    example: 5000000,
  })
  @Expose()
  fee: number;

  @ApiProperty({
    description: 'Tổng tiền',
    example: *********,
  })
  @Expose()
  totalAmount: number;

  @ApiPropertyOptional({
    description: 'Thông tin đối tác',
    example: 'Công ty TNHH Công nghệ XYZ',
  })
  @Expose()
  partnerInfo?: string;

  @ApiPropertyOptional({
    description: 'Mục đích sử dụng',
    example: 'Nâng cấp hạ tầng IT',
  })
  @Expose()
  purpose?: string;

  @ApiPropertyOptional({
    description: 'Thông tin thanh toán',
    example: { method: 'bank_transfer', account: '*********', bank: 'Vietcombank' },
  })
  @Expose()
  chargeInfo?: Record<string, any>;

  @ApiProperty({
    description: 'Trạng thái đề xuất',
    enum: ProposalStatus,
  })
  @Expose()
  status: ProposalStatus;

  @ApiProperty({
    description: 'Ngày đề xuất',
    example: '2025-01-15T10:30:00Z',
  })
  @Expose()
  proposalDate: Date;

  @ApiPropertyOptional({
    description: 'Ngày phê duyệt/từ chối',
    example: '2025-01-20T14:30:00Z',
  })
  @Expose()
  reviewedAt?: Date;

  @ApiPropertyOptional({
    description: 'Lý do từ chối',
    example: 'Ngân sách không đủ',
  })
  @Expose()
  rejectionReason?: string;

  @ApiProperty({
    description: 'Mức độ ưu tiên',
    example: 2,
  })
  @Expose()
  priority: number;

  @ApiPropertyOptional({
    description: 'Danh mục đề xuất',
    example: 'IT Infrastructure',
  })
  @Expose()
  category?: string;

  @ApiPropertyOptional({
    description: 'File đính kèm',
    example: ['https://example.com/file1.pdf'],
    type: [String],
  })
  @Expose()
  attachments?: string[];

  @ApiPropertyOptional({
    description: 'Ngân sách dự kiến',
    example: 100000000,
  })
  @Expose()
  estimatedBudget?: number;

  @ApiPropertyOptional({
    description: 'Ngày dự kiến hoàn thành',
    example: '2025-03-31T23:59:59Z',
  })
  @Expose()
  expectedCompletionDate?: Date;

  @ApiProperty({
    description: 'Ngày tạo',
    example: '2025-01-15T10:30:00Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Ngày cập nhật',
    example: '2025-01-20T14:30:00Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Người đề xuất',
    type: ProposerSummaryDto,
  })
  @Expose()
  @Type(() => ProposerSummaryDto)
  proposer?: ProposerSummaryDto;

  @ApiPropertyOptional({
    description: 'Người phê duyệt',
    type: ReviewerSummaryDto,
  })
  @Expose()
  @Type(() => ReviewerSummaryDto)
  reviewer?: ReviewerSummaryDto;

  @ApiPropertyOptional({
    description: 'Bộ phận',
    type: DepartmentSummaryDto,
  })
  @Expose()
  @Type(() => DepartmentSummaryDto)
  department?: DepartmentSummaryDto;

  // Computed properties
  @ApiProperty({
    description: 'Có quá hạn không',
    example: false,
  })
  @Expose()
  get isOverdue(): boolean {
    if (!this.expectedCompletionDate) return false;
    return new Date() > this.expectedCompletionDate && this.status === ProposalStatus.PENDING;
  }

  @ApiProperty({
    description: 'Số ngày từ khi đề xuất',
    example: 5,
  })
  @Expose()
  get daysSinceProposal(): number {
    return Math.floor((Date.now() - this.proposalDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  @ApiProperty({
    description: 'Tổng tiền định dạng',
    example: '105,000,000 VND',
  })
  @Expose()
  get formattedTotalAmount(): string {
    return `${this.totalAmount.toLocaleString()} VND`;
  }

  // Exclude sensitive fields
  @Exclude()
  deletedAt?: Date;

  @Exclude()
  deletedBy?: string;

  @Exclude()
  metadata?: Record<string, any>;
}
