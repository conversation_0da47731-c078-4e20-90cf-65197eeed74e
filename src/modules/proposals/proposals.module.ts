import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProposalsService } from './proposals.service';
import { ProposalsController } from './proposals.controller';
import { InvoiceProposals } from '@/entities/invoice-proposals.entity';
import { Brand } from '@/entities/brand.entity';
import { Team } from '@/entities/team.entity';
import { User } from '@/entities/user.entity';
import { Department } from '@/entities/department.entity';
import { CommonServicesModule } from '@/common/services/common-services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([InvoiceProposals, User, Department, Brand, Team]),
    CommonServicesModule,
  ],
  controllers: [ProposalsController],
  providers: [ProposalsService],
  exports: [ProposalsService],
})
export class ProposalsModule {}
