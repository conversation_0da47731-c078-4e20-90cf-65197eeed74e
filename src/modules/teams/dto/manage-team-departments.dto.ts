import { IsArray, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AddDepartmentsToTeamDto {
  @ApiProperty({
    description: 'Array of Department IDs to add to the team',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID(4, { each: true })
  departmentIds: string[];
}

export class RemoveDepartmentsFromTeamDto {
  @ApiProperty({
    description: 'Array of Department IDs to remove from the team',
    example: ['123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID(4, { each: true })
  departmentIds: string[];
}
