import { IsString, <PERSON>U<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateTeamDto {
  @ApiProperty({
    description: 'Name of the team',
    example: 'Development Team',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: 'Description of the team',
    example: 'Team responsible for software development',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Array of Department IDs that this team belongs to',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID(4, { each: true })
  departmentIds: string[];
}
