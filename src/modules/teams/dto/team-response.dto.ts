import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';

class DepartmentSummaryDto {
  @Expose()
  id: string;

  @Expose()
  name: string;
}

class UserSummaryDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Username',
    example: 'john_doe',
  })
  @Expose()
  username: string;

  @ApiProperty({
    description: 'Full name',
    example: '<PERSON>',
  })
  @Expose()
  fullName: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiPropertyOptional({
    description: 'Phone number',
    example: '+1234567890',
  })
  @Expose()
  phone?: string;
}

export class TeamResponseDto {
  @ApiProperty({
    description: 'Team ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Team name',
    example: 'Development Team',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Description of the team',
    example: 'Team responsible for software development',
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Created by',
    example: 'admin',
  })
  @Expose()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Updated by',
    example: 'admin',
  })
  @Expose()
  updatedBy?: string;

  @ApiPropertyOptional({
    description: 'List of departments this team belongs to',
    type: [DepartmentSummaryDto],
  })
  @Expose()
  @Type(() => DepartmentSummaryDto)
  departments?: DepartmentSummaryDto[];

  @ApiPropertyOptional({
    description: 'List of users in this team',
    type: [UserSummaryDto],
  })
  @Expose()
  @Type(() => UserSummaryDto)
  users?: UserSummaryDto[];

  @Exclude()
  deletedAt?: Date;

  @Exclude()
  deletedBy?: string;

  @Exclude()
  metadata?: Record<string, any>;
}
