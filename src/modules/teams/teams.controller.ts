import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { TeamsService } from './teams.service';
import { CreateTeamDto, UpdateTeamDto, TeamResponseDto, TeamQueryDto, AddDepartmentsToTeamDto, RemoveDepartmentsFromTeamDto } from './dto';
import { PaginatedResult } from '@/common/dto/pagination.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { RoleType, PermissionsType } from '@/common/constants';
import { CurrentUser } from '@/common/decorators';

@ApiTags('Teams')
@Controller('teams')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class TeamsController {
  constructor(private readonly teamsService: TeamsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new team' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Team created successfully',
    type: TeamResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Department not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Team name already exists in this department',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.CREATE)
  async create(@Body() createTeamDto: CreateTeamDto, @Request() req: any): Promise<TeamResponseDto> {
    const team = await this.teamsService.create(createTeamDto, req.user.fullName || req.user.username, req.user.id);
    return plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true });
  }

  @Get()
  @ApiOperation({ summary: 'Get all teams with pagination and search' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Teams retrieved successfully',
    type: [TeamResponseDto],
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  @ApiQuery({ name: 'departmentId', required: false, type: String, description: 'Filter by department ID' })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async findAll(
    @Query() teamQueryDto: TeamQueryDto,
  ): Promise<PaginatedResult<TeamResponseDto>> {
    const result = await this.teamsService.findAll(teamQueryDto, teamQueryDto.departmentId);
    
    return {
      ...result,
      data: result.data.map(team => 
        plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('by-department/:departmentId')
  @ApiOperation({ summary: 'Get teams by department ID' })
  @ApiParam({ name: 'departmentId', description: 'Department ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Teams retrieved successfully',
    type: [TeamResponseDto],
  })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async findByDepartment(@Param('departmentId', ParseUUIDPipe) departmentId: string): Promise<TeamResponseDto[]> {
    const teams = await this.teamsService.findByDepartment(departmentId);
    return teams.map(team => 
      plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true })
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get team by ID' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Team retrieved successfully',
    type: TeamResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Team not found',
  })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<TeamResponseDto> {
    const team = await this.teamsService.findOne(id);
    return plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true });
  }

  @Get(':id/stats')
  @ApiOperation({ summary: 'Get team statistics' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Team statistics retrieved successfully',
  })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  async getStats(@Param('id', ParseUUIDPipe) id: string) {
    return await this.teamsService.getTeamStats(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update team by ID' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Team updated successfully',
    type: TeamResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Team not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Department not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Team name already exists in this department',
  })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTeamDto: UpdateTeamDto,
    @CurrentUser() currentUser: any,
  ): Promise<TeamResponseDto> {
    const team = await this.teamsService.update(id, updateTeamDto, currentUser.fullName || currentUser.username);
    return plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete team by ID' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Team deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Team not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Cannot delete team with existing users',
  })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.DELETE)
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() currentUser: any): Promise<void> {
    await this.teamsService.remove(id, currentUser.fullName || currentUser.username);
  }

  @Post(':id/departments/add')
  @ApiOperation({ summary: 'Add departments to team' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Departments added to team successfully',
    type: TeamResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Team or departments not found',
  })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  async addDepartments(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() addDepartmentsDto: AddDepartmentsToTeamDto,
    @CurrentUser() currentUser: any,
  ): Promise<TeamResponseDto> {
    const team = await this.teamsService.addDepartments(
      id,
      addDepartmentsDto.departmentIds,
      currentUser.fullName || currentUser.username
    );
    return plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true });
  }

  @Post(':id/departments/remove')
  @ApiOperation({ summary: 'Remove departments from team' })
  @ApiParam({ name: 'id', description: 'Team ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Departments removed from team successfully',
    type: TeamResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Team not found',
  })
  @ApiBearerAuth('JWT-auth')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  async removeDepartments(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() removeDepartmentsDto: RemoveDepartmentsFromTeamDto,
    @CurrentUser() currentUser: any,
  ): Promise<TeamResponseDto> {
    const team = await this.teamsService.removeDepartments(
      id,
      removeDepartmentsDto.departmentIds,
      currentUser.fullName || currentUser.username
    );
    return plainToClass(TeamResponseDto, team, { excludeExtraneousValues: true });
  }
}
