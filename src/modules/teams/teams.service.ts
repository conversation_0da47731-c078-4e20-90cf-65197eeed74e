import { Injectable, NotFoundException, ConflictException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Team } from '@/entities/team.entity';
import { Department } from '@/entities/department.entity';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { UserContextService, UserContext } from '@/common/services/user-context.service';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class TeamsService {
  constructor(
    @InjectRepository(Team)
    private readonly teamRepository: Repository<Team>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    private readonly userContextService: UserContextService,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createTeamDto: CreateTeamDto, createdBy?: string, currentUserId?: string): Promise<Team> {
    this.logger.info(
      `Creating new team: ${createTeamDto.name} in departments: ${createTeamDto.departmentIds.join(', ')}`,
      'TeamsService',
      { name: createTeamDto.name, departmentIds: createTeamDto.departmentIds, createdBy }
    );

    // Get current user context for permission checking
    let userContext: UserContext | null = null;
    if (currentUserId) {
      userContext = await this.userContextService.getUserContext(currentUserId);
    }

    // Check if all departments exist
    const departments = await this.departmentRepository.findByIds(createTeamDto.departmentIds);
    if (departments.length !== createTeamDto.departmentIds.length) {
      const foundIds = departments.map(d => d.id);
      const missingIds = createTeamDto.departmentIds.filter(id => !foundIds.includes(id));
      throw new BadRequestException(`Departments not found: ${missingIds.join(', ')}`);
    }

    // Check if current user can create team in these departments
    if (userContext) {
      for (const departmentId of createTeamDto.departmentIds) {
        if (!this.userContextService.canManageDepartment(userContext, departmentId)) {
          throw new ForbiddenException(`You can only create teams within your own departments. Access denied for department: ${departmentId}`);
        }
      }
    }

    // Check if team name already exists (globally unique now)
    const existingTeam = await this.teamRepository.findOne({
      where: { name: createTeamDto.name },
    });
    if (existingTeam) {
      throw new ConflictException('Team name already exists');
    }

    // Create team
    const team = this.teamRepository.create({
      name: createTeamDto.name,
      description: createTeamDto.description,
      createdBy,
    });

    // Save team first
    const savedTeam = await this.teamRepository.save(team);

    // Set departments relationship
    savedTeam.departments = departments;
    await this.teamRepository.save(savedTeam);

    return savedTeam;
  }

  async findAll(paginationDto: PaginationDto, departmentId?: string): Promise<PaginatedResult<Team>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;

    const queryBuilder = this.teamRepository.createQueryBuilder('team')
      .leftJoinAndSelect('team.departments', 'departments')
      .leftJoinAndSelect('team.users', 'users');

    // Filter by department if provided
    if (departmentId) {
      queryBuilder.where('departments.id = :departmentId', { departmentId });
    }

    // Add search functionality
    if (search) {
      const searchCondition = departmentId
        ? 'team.name LIKE :search OR team.description LIKE :search'
        : '(team.name LIKE :search OR team.description LIKE :search OR departments.name LIKE :search)';

      if (departmentId) {
        queryBuilder.andWhere(searchCondition, { search: `%${search}%` });
      } else {
        queryBuilder.andWhere(searchCondition, { search: `%${search}%` });
      }
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`team.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [teams, total] = await queryBuilder.getManyAndCount();

    return createPaginatedResult(teams, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<Team> {
    const team = await this.teamRepository.findOne({
      where: { id },
      relations: ['departments', 'users'],
    });

    if (!team) {
      throw new NotFoundException(`Team with ID ${id} not found`);
    }

    return team;
  }

  async findByDepartment(departmentId: string): Promise<Team[]> {
    return await this.teamRepository
      .createQueryBuilder('team')
      .leftJoinAndSelect('team.departments', 'departments')
      .leftJoinAndSelect('team.users', 'users')
      .where('departments.id = :departmentId', { departmentId })
      .orderBy('team.name', 'ASC')
      .getMany();
  }

  async update(id: string, updateTeamDto: UpdateTeamDto, updatedBy?: string): Promise<Team> {
    const team = await this.findOne(id);

    // Check if departments exist if being updated
    if (updateTeamDto.departmentIds && updateTeamDto.departmentIds.length > 0) {
      const departments = await this.departmentRepository.findByIds(updateTeamDto.departmentIds);
      if (departments.length !== updateTeamDto.departmentIds.length) {
        const foundIds = departments.map(d => d.id);
        const missingIds = updateTeamDto.departmentIds.filter(id => !foundIds.includes(id));
        throw new BadRequestException(`Departments not found: ${missingIds.join(', ')}`);
      }

      // Update departments relationship
      team.departments = departments;
    }

    // Check if team name already exists (globally unique)
    if (updateTeamDto.name && updateTeamDto.name !== team.name) {
      const existingTeam = await this.teamRepository.findOne({
        where: { name: updateTeamDto.name },
      });
      if (existingTeam && existingTeam.id !== id) {
        throw new ConflictException('Team name already exists');
      }
    }

    // Update team properties
    if (updateTeamDto.name) team.name = updateTeamDto.name;
    if (updateTeamDto.description !== undefined) team.description = updateTeamDto.description;
    team.updatedBy = updatedBy;

    return await this.teamRepository.save(team);
  }

  /**
   * Add departments to team
   */
  async addDepartments(teamId: string, departmentIds: string[], updatedBy?: string): Promise<Team> {
    const team = await this.findOne(teamId);

    // Check if departments exist
    const departments = await this.departmentRepository.findByIds(departmentIds);
    if (departments.length !== departmentIds.length) {
      const foundIds = departments.map(d => d.id);
      const missingIds = departmentIds.filter(id => !foundIds.includes(id));
      throw new BadRequestException(`Departments not found: ${missingIds.join(', ')}`);
    }

    // Get current department IDs
    const currentDepartmentIds = team.departments.map(d => d.id);

    // Add only new departments
    const newDepartments = departments.filter(d => !currentDepartmentIds.includes(d.id));
    team.departments = [...team.departments, ...newDepartments];
    team.updatedBy = updatedBy;

    return await this.teamRepository.save(team);
  }

  /**
   * Remove departments from team
   */
  async removeDepartments(teamId: string, departmentIds: string[], updatedBy?: string): Promise<Team> {
    const team = await this.findOne(teamId);

    // Filter out departments to remove
    team.departments = team.departments.filter(d => !departmentIds.includes(d.id));
    team.updatedBy = updatedBy;

    return await this.teamRepository.save(team);
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    const team = await this.findOne(id);
    
    // Check if team has users
    if (team.users && team.users.length > 0) {
      throw new ConflictException('Cannot delete team with existing users');
    }
    
    // Soft delete
    team.deletedBy = deletedBy;
    await this.teamRepository.softRemove(team);
  }

  async getTeamStats(id: string): Promise<{
    totalUsers: number;
    departmentNames: string[];
  }> {
    const team = await this.teamRepository
      .createQueryBuilder('team')
      .leftJoinAndSelect('team.departments', 'departments')
      .leftJoinAndSelect('team.users', 'users')
      .where('team.id = :id', { id })
      .getOne();

    if (!team) {
      throw new NotFoundException(`Team with ID ${id} not found`);
    }

    return {
      totalUsers: team.users?.length || 0,
      departmentNames: team.departments?.map(d => d.name) || [],
    };
  }
}
