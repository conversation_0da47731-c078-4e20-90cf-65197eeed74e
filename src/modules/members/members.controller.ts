import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { MembersService } from './members.service';
import { CreateMemberDto, UpdateMemberDto, BulkCreateMemberDto, BulkCreateMemberResponseDto } from './dto';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';

@ApiTags('Members')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('members')
export class MembersController {
  constructor(private readonly membersService: MembersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new member' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Member created successfully',
  })
  async create(
    @Body() createMemberDto: CreateMemberDto,
    @Request() req: any,
  ) {
    return await this.membersService.create(createMemberDto, req.user?.username);
  }

  @Post('bulk')
  @ApiOperation({ summary: 'Bulk create multiple members' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Members bulk created successfully',
    type: BulkCreateMemberResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async bulkCreate(
    @Body() bulkCreateMemberDto: BulkCreateMemberDto,
    @Request() req: any,
  ): Promise<BulkCreateMemberResponseDto> {
    return await this.membersService.bulkCreate(
      bulkCreateMemberDto,
      req.user?.username
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all members with pagination' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Members retrieved successfully',
  })
  async findAll(@Query() paginationDto: PaginationDto) {
    return await this.membersService.findAll(paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a member by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Member retrieved successfully',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return await this.membersService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a member' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Member updated successfully',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMemberDto: UpdateMemberDto,
    @Request() req: any,
  ) {
    return await this.membersService.update(id, updateMemberDto, req.user?.username);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a member' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Member deleted successfully',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.membersService.remove(id, req.user?.username);
  }
}
