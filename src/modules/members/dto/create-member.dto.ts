import { IsString, IsOptional, <PERSON><PERSON><PERSON>ber, IsDateString, Max<PERSON>eng<PERSON>, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateMemberDto {
  @ApiProperty({
    description: 'Brand ID associated with the member',
    example: '1',
  })
  @IsString()
  brand_id: string;

  @ApiProperty({
    description: 'Agency code for the member',
    example: 'AG001',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  agency_code: string;

  @ApiProperty({
    description: 'Username of the member',
    example: 'johndoe123',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  username: string;

  @ApiPropertyOptional({
    description: 'Full name of the member',
    example: '<PERSON>',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  fullname?: string;

  @ApiPropertyOptional({
    description: 'Level of the member',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  level?: number;

  @ApiPropertyOptional({
    description: 'Member level description',
    example: 'Gold',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  member_level?: string;

  @ApiProperty({
    description: 'Join date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDateString()
  join_at: string;
}
