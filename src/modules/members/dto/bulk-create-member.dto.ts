import { IsArray, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateMemberDto } from './create-member.dto';

export class BulkCreateMemberDto {
  @ApiProperty({
    description: 'Array of members to create',
    type: [CreateMemberDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateMemberDto)
  members: CreateMemberDto[];
}

export class BulkCreateMemberResponseDto {
  @ApiProperty({
    description: 'Number of successfully created members',
    example: 95,
  })
  success: number;

  @ApiProperty({
    description: 'Number of failed member creations',
    example: 5,
  })
  failed: number;

  @ApiProperty({
    description: 'Total number of members processed',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Array of error messages for failed creations',
    type: [String],
    example: ['Username johndoe123 already exists', 'Invalid agency code AG999'],
  })
  errors: string[];

  @ApiProperty({
    description: 'Array of successfully created member IDs',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-426614174000', '456e7890-e89b-12d3-a456-426614174001'],
  })
  createdIds: string[];
}
