import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Member } from '@/entities/member.entity';
import { Agency } from '@/entities/agency.entity';
import { CreateMemberDto } from './dto/create-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { BulkCreateMemberDto, BulkCreateMemberResponseDto } from './dto/bulk-create-member.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class MembersService {
  constructor(
    @InjectRepository(Member)
    private readonly memberRepository: Repository<Member>,
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    private readonly dataSource: DataSource,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createMemberDto: CreateMemberDto, createdBy?: string): Promise<Member> {
    this.logger.info(
      `Creating new member: ${createMemberDto.username}`,
      'MembersService',
      { username: createMemberDto.username, agency_code: createMemberDto.agency_code, createdBy }
    );

    try {
      // Check if username already exists
      const existingMember = await this.memberRepository.findOne({
        where: { username: createMemberDto.username },
      });
      if (existingMember) {
        throw new ConflictException(`Username ${createMemberDto.username} already exists`);
      }

      // Verify agency exists
      const agency = await this.agencyRepository.findOne({
        where: { code: createMemberDto.agency_code },
      });
      if (!agency) {
        throw new NotFoundException(`Agency with code ${createMemberDto.agency_code} not found`);
      }

      const member = this.memberRepository.create({
        ...createMemberDto,
        join_at: new Date(createMemberDto.join_at),
        level: createMemberDto.level || 1,
        deposit_times: 0,
        deposit_total: 0,
        withdraw_total: 0,
        bet_total: 0,
        valid_bet: 0,
        payout: 0,
        bonus: 0,
        deduction: 0,
        profit: 0,
        createdBy,
      });

      const savedMember = await this.memberRepository.save(member);

      this.logger.info(
        `Member created successfully: ${savedMember.username}`,
        'MembersService',
        { memberId: savedMember.id, username: savedMember.username }
      );

      return savedMember;
    } catch (error) {
      this.logger.error(
        `Failed to create member: ${createMemberDto.username}`,
        error,
        'MembersService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<Member>> {
    this.logger.info('Fetching all members', 'MembersService', paginationDto);

    try {
      const { page = 1, limit = 10, sortBy, sortOrder } = paginationDto;
      const skip = (page - 1) * limit;

      const queryBuilder = this.memberRepository.createQueryBuilder('member')
        .leftJoinAndSelect('member.agency', 'agency');

      // Apply sorting
      if (sortBy) {
        queryBuilder.orderBy(`member.${sortBy}`, sortOrder);
      } else {
        queryBuilder.orderBy('member.createdAt', 'DESC');
      }

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      const [members, total] = await queryBuilder.getManyAndCount();

      this.logger.info(
        `Successfully fetched ${members.length} members`,
        'MembersService',
        { total, page, limit }
      );

      return createPaginatedResult(members, total, page, limit);
    } catch (error) {
      this.logger.error('Failed to fetch members', error, 'MembersService');
      throw error;
    }
  }

  async findOne(id: string): Promise<Member> {
    this.logger.info(`Fetching member by ID: ${id}`, 'MembersService');

    try {
      const member = await this.memberRepository.findOne({
        where: { id },
        relations: ['agency', 'transactions'],
      });

      if (!member) {
        this.logger.warn(`Member not found: ${id}`, 'MembersService');
        throw new NotFoundException('Member not found');
      }

      return member;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch member: ${id}`, error, 'MembersService');
      throw error;
    }
  }

  async update(id: string, updateMemberDto: UpdateMemberDto, updatedBy?: string): Promise<Member> {
    this.logger.info(`Updating member: ${id}`, 'MembersService', { updateMemberDto, updatedBy });

    try {
      const member = await this.findOne(id);

      // Update member
      Object.assign(member, updateMemberDto, { updatedBy });
      // TODO: Fix this when Member entity is properly mapped
      // if (updateMemberDto.joinDate) {
      //   member.join_at = new Date(updateMemberDto.joinDate);
      // }
      
      const updatedMember = await this.memberRepository.save(member);

      this.logger.info(
        `Member updated successfully: ${updatedMember}`,
        'MembersService',
        { memberId: id }
      );

      return updatedMember;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Failed to update member: ${id}`, error, 'MembersService');
      throw error;
    }
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    this.logger.info(`Deleting member: ${id}`, 'MembersService', { deletedBy });

    try {
      const member = await this.findOne(id);

      // Soft delete
      member.deletedBy = deletedBy;
      await this.memberRepository.softDelete(id);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to delete member: ${id}`, error, 'MembersService');
      throw error;
    }
  }

  /**
   * Bulk create members with optimized performance
   */
  async bulkCreate(bulkCreateDto: BulkCreateMemberDto, createdBy?: string): Promise<BulkCreateMemberResponseDto> {
    this.logger.info(
      `Starting bulk create for ${bulkCreateDto.members.length} members`,
      'MembersService',
      { count: bulkCreateDto.members.length, createdBy }
    );

    const response: BulkCreateMemberResponseDto = {
      success: 0,
      failed: 0,
      total: bulkCreateDto.members.length,
      errors: [],
      createdIds: [],
    };

    // Use transaction for better performance and data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate all usernames are unique in the batch
      const usernames = bulkCreateDto.members.map(m => m.username);
      const duplicateUsernames = usernames.filter((username, index) => usernames.indexOf(username) !== index);

      if (duplicateUsernames.length > 0) {
        response.errors.push(`Duplicate usernames in batch: ${duplicateUsernames.join(', ')}`);
      }

      // Check for existing usernames in database
      const existingMembers = await queryRunner.manager.find(Member, {
        where: usernames.map(username => ({ username })),
        select: ['username'],
      });

      const existingUsernames = existingMembers.map(m => m.username);

      // Validate agency codes exist
      const agencyCodes = [...new Set(bulkCreateDto.members.map(m => m.agency_code))];
      const existingAgencies = await queryRunner.manager.find(Agency, {
        where: agencyCodes.map(code => ({ code })),
        select: ['code'],
      });

      const existingAgencyCodes = existingAgencies.map(a => a.code);

      // Filter valid members
      const validMembers = bulkCreateDto.members.filter(member => {
        const isUsernameValid = !existingUsernames.includes(member.username) && !duplicateUsernames.includes(member.username);
        const isAgencyValid = existingAgencyCodes.includes(member.agency_code);

        if (!isUsernameValid) {
          response.errors.push(`Username ${member.username} already exists or is duplicate`);
          response.failed++;
          return false;
        }

        if (!isAgencyValid) {
          response.errors.push(`Agency code ${member.agency_code} not found`);
          response.failed++;
          return false;
        }

        return true;
      });

      if (validMembers.length > 0) {
        // Prepare members data for bulk insert
        const membersToCreate = validMembers.map(memberData => {
          const memberEntity: any = {
            brand_id: memberData.brand_id,
            agency_code: memberData.agency_code,
            username: memberData.username,
            level: memberData.level || 1,
            deposit_times: 0,
            deposit_total: 0,
            withdraw_total: 0,
            bet_total: 0,
            valid_bet: 0,
            payout: 0,
            bonus: 0,
            deduction: 0,
            profit: 0,
            join_at: new Date(memberData.join_at),
            createdBy,
          };

          if (memberData.fullname) {
            memberEntity.fullname = memberData.fullname;
          }
          if (memberData.member_level) {
            memberEntity.member_level = memberData.member_level;
          }

          return memberEntity;
        });

        // Use bulk insert for better performance
        const insertResult = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(Member)
          .values(membersToCreate)
          .execute();

        response.success = validMembers.length;
        response.createdIds = insertResult.identifiers.map(identifier => identifier.id);

        this.logger.info(
          `Successfully bulk created ${response.success} members`,
          'MembersService',
          { success: response.success, failed: response.failed }
        );
      }

      await queryRunner.commitTransaction();

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        'Failed to bulk create members',
        error,
        'MembersService'
      );

      // If bulk insert fails, add error to response
      response.failed = bulkCreateDto.members.length;
      response.success = 0;
      response.errors.push(`Bulk insert failed: ${error.message}`);

    } finally {
      await queryRunner.release();
    }

    return response;
  }
}
