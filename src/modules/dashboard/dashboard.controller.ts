import {
  Controller,
  Get,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { DashboardService } from './dashboard.service';
import { 
  DashboardChartDto, 
  FTDChartResponseDto, 
  TotalBetsChartResponseDto,
  DepositorsChartResponseDto,
  RevenueChartResponseDto 
} from './dto/dashboard-chart.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { PermissionsType, RoleType } from '@/common/constants';

@ApiTags('Dashboard Charts')
@Controller('dashboard')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('charts/ftd')
  @ApiOperation({ summary: 'Lấy dữ liệu biểu đồ FTD (First Time Deposit)' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Dữ liệu biểu đồ FTD lấy thành công',
    type: [FTDChartResponseDto]
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getFTDChartData(@Query() chartDto: DashboardChartDto): Promise<FTDChartResponseDto[]> {
    return this.dashboardService.getFTDChartData(chartDto);
  }

  @Get('charts/total-bets')
  @ApiOperation({ summary: 'Lấy dữ liệu biểu đồ tổng cược' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Dữ liệu biểu đồ tổng cược lấy thành công',
    type: [TotalBetsChartResponseDto]
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getTotalBetsChartData(@Query() chartDto: DashboardChartDto): Promise<TotalBetsChartResponseDto[]> {
    return this.dashboardService.getTotalBetsChartData(chartDto);
  }

  @Get('charts/depositors')
  @ApiOperation({ summary: 'Lấy dữ liệu biểu đồ số người nạp tiền' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Dữ liệu biểu đồ số người nạp tiền lấy thành công',
    type: [DepositorsChartResponseDto]
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getDepositorsChartData(@Query() chartDto: DashboardChartDto): Promise<DepositorsChartResponseDto[]> {
    return this.dashboardService.getDepositorsChartData(chartDto);
  }

  @Get('charts/revenue')
  @ApiOperation({ summary: 'Lấy dữ liệu biểu đồ doanh thu' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Dữ liệu biểu đồ doanh thu lấy thành công',
    type: [RevenueChartResponseDto]
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getRevenueChartData(@Query() chartDto: DashboardChartDto): Promise<RevenueChartResponseDto[]> {
    return this.dashboardService.getRevenueChartData(chartDto);
  }

  @Get('charts/summary')
  @ApiOperation({ summary: 'Lấy tóm tắt tất cả biểu đồ dashboard' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Tóm tắt dashboard lấy thành công'
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getDashboardSummary(@Query() chartDto: DashboardChartDto) {
    const [ftdData, totalBetsData, depositorsData, revenueData] = await Promise.all([
      this.dashboardService.getFTDChartData(chartDto),
      this.dashboardService.getTotalBetsChartData(chartDto),
      this.dashboardService.getDepositorsChartData(chartDto),
      this.dashboardService.getRevenueChartData(chartDto)
    ]);

    return {
      ftd: ftdData,
      totalBets: totalBetsData,
      depositors: depositorsData,
      revenue: revenueData,
      summary: {
        totalFTD: ftdData.reduce((sum, item) => sum + item.ftd_count, 0),
        totalFTDAmount: ftdData.reduce((sum, item) => sum + item.ftd_amount, 0),
        totalBets: totalBetsData.reduce((sum, item) => sum + item.total_bets, 0),
        totalBetAmount: totalBetsData.reduce((sum, item) => sum + item.total_bet_amount, 0),
        totalDepositors: depositorsData.reduce((sum, item) => sum + item.depositors_count, 0),
        totalRevenue: revenueData.reduce((sum, item) => sum + item.total_revenue, 0),
        totalProfit: revenueData.reduce((sum, item) => sum + item.profit, 0),
        generatedAt: new Date().toISOString()
      }
    };
  }
} 