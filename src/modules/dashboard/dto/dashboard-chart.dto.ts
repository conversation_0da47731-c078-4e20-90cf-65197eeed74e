import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsEnum } from 'class-validator';

export enum ChartType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export class DashboardChartDto {
  @ApiProperty({ description: 'Từ ngày', required: false })
  @IsOptional()
  @IsDateString()
  from_date?: string;

  @ApiProperty({ description: 'Đến ngày', required: false })
  @IsOptional()
  @IsDateString()
  to_date?: string;

  @ApiProperty({ description: 'ID bộ phận', required: false })
  @IsOptional()
  @IsString()
  department_id?: string;

  @ApiProperty({ description: 'Loại biểu đồ', enum: ChartType, default: ChartType.DAILY })
  @IsOptional()
  @IsEnum(ChartType)
  chart_type?: ChartType;
}

export class FTDChartResponseDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Số người nạp tiền lần đầu' })
  ftd_count: number;

  @ApiProperty({ description: 'Tổng tiền FTD' })
  ftd_amount: number;

  @ApiProperty({ description: 'Tỷ lệ FTD' })
  ftd_rate: number;
}

export class TotalBetsChartResponseDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Tổng số cược' })
  total_bets: number;

  @ApiProperty({ description: 'Tổng tiền cược' })
  total_bet_amount: number;

  @ApiProperty({ description: 'Tổng tiền thắng' })
  total_win_amount: number;

  @ApiProperty({ description: 'Tổng tiền thua' })
  total_loss_amount: number;

  @ApiProperty({ description: 'Tỷ lệ thắng' })
  win_rate: number;
}

export class DepositorsChartResponseDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Số người nạp tiền' })
  depositors_count: number;

  @ApiProperty({ description: 'Tổng tiền nạp' })
  total_deposit_amount: number;

  @ApiProperty({ description: 'Số giao dịch nạp' })
  deposit_transactions: number;

  @ApiProperty({ description: 'Tiền nạp trung bình' })
  avg_deposit_amount: number;
}

export class RevenueChartResponseDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Doanh thu từ deposits' })
  deposit_revenue: number;

  @ApiProperty({ description: 'Doanh thu từ bets' })
  bet_revenue: number;

  @ApiProperty({ description: 'Tổng doanh thu' })
  total_revenue: number;

  @ApiProperty({ description: 'Lợi nhuận' })
  profit: number;

  @ApiProperty({ description: 'Tỷ lệ lợi nhuận (%)' })
  profit_margin: number;
} 