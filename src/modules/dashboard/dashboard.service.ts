import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/entities';
import { 
  DashboardChartDto, 
  FTDChartResponseDto, 
  TotalBetsChartResponseDto,
  DepositorsChartResponseDto,
  RevenueChartResponseDto,
  ChartType
} from './dto/dashboard-chart.dto';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async getDashboardStats() {
    const totalUsers = await this.userRepository.count();
    const activeUsers = await this.userRepository.count();
    return {
      totalUsers,
      activeUsers,
      inactiveUsers: totalUsers - activeUsers,
    };
  }

  async getFTDChartData(chartDto: DashboardChartDto): Promise<FTDChartResponseDto[]> {
    return [
      {
        date: '2025-01-01',
        ftd_count: 10,
        ftd_amount: 1000000,
        ftd_rate: 5.5,
      },
    ];
  }

  async getTotalBetsChartData(chartDto: DashboardChartDto): Promise<TotalBetsChartResponseDto[]> {
    return [
      {
        date: '2025-01-01',
        total_bets: 100,
        total_bet_amount: 5000000,
        total_win_amount: 4500000,
        total_loss_amount: 500000,
        win_rate: 90.0,
      },
    ];
  }

  async getDepositorsChartData(chartDto: DashboardChartDto): Promise<DepositorsChartResponseDto[]> {
    return [
      {
        date: '2025-01-01',
        depositors_count: 50,
        total_deposit_amount: 10000000,
        avg_deposit_amount: 200000,
        deposit_transactions: 75,
      },
    ];
  }

  async getRevenueChartData(chartDto: DashboardChartDto): Promise<RevenueChartResponseDto[]> {
    return [
      {
        date: '2025-01-01',
        total_revenue: 2000000,
        deposit_revenue: 1500000,
        bet_revenue: 500000,
        profit: 200000,
        profit_margin: 10.0,
      },
    ];
  }

  private getDateFormat(chartType: ChartType): string {
    switch (chartType) {
      case ChartType.DAILY:
        return 'DATE(created_at)';
      case ChartType.WEEKLY:
        return 'YEARWEEK(created_at)';
      case ChartType.MONTHLY:
        return 'DATE_FORMAT(created_at, "%Y-%m")';
      default:
        return 'DATE(created_at)';
    }
  }
}
