# NH88 CMS Backend

Hệ thống Content Management System (CMS) Backend được xây dựng bằng NestJS với kiến trúc Role-Based Access Control (RBAC) và hệ thống quản lý affiliate marketing.

## 📋 Mục lục

- [Tổng quan](#tổng-quan)
- [Kiến trúc hệ thống](#kiến-trúc-hệ-thống)
- [T<PERSON>h năng chính](#tính-năng-chính)
- [Cài đặt và chạy](#cài-đặt-và-chạy)
- [API Documentation](#api-documentation)
- [Cấu trúc database](#cấu-trúc-database)
- [Hệ thống phân quyền](#hệ-thống-phân-quyền)
- [Audit Logging](#audit-logging)
- [Affiliate System](#affiliate-system)
- [Deployment](#deployment)

## 🎯 Tổng quan

NH88 CMS Backend là một hệ thống quản lý nội dung hiện đại được xây dựng với:

- **Framework**: NestJS (Node.js)
- **Database**: MySQL với TypeORM
- **Authentication**: JWT + Passport
- **Authorization**: RBAC (Role-Based Access Control)
- **Documentation**: Swagger/OpenAPI
- **Logging**: Winston với rotation
- **Validation**: Class-validator
- **Testing**: Jest

## 🏗️ Kiến trúc hệ thống

### Cấu trúc thư mục

```
src/
├── common/                 # Shared utilities
│   ├── constants/         # Constants và enums
│   ├── decorators/        # Custom decorators
│   ├── guards/            # Authentication/Authorization guards
│   ├── interceptors/      # HTTP interceptors
│   ├── logger/            # Logging system
│   ├── services/          # Shared services
│   └── utils/             # Utility functions
├── config/                # Configuration
├── database/              # Database seeders
├── entities/              # TypeORM entities
├── modules/               # Feature modules
│   ├── auth/             # Authentication
│   ├── users/            # User management
│   ├── roles/            # Role management
│   ├── permissions/      # Permission management
│   ├── departments/      # Department management
│   ├── teams/            # Team management
│   ├── affiliate/        # Affiliate system
│   ├── audit/            # Audit logging
│   └── upload/           # File upload
└── main.ts               # Application entry point
```

### Module Architecture

Hệ thống được chia thành các module độc lập:

- **Auth Module**: Xác thực và phân quyền
- **Users Module**: Quản lý người dùng
- **Roles Module**: Quản lý vai trò
- **Permissions Module**: Quản lý quyền hạn
- **Departments Module**: Quản lý phòng ban
- **Teams Module**: Quản lý nhóm
- **Affiliate Module**: Hệ thống affiliate marketing
- **Audit Module**: Ghi log hoạt động
- **Upload Module**: Upload file

## ✨ Tính năng chính

### 🔐 Authentication & Authorization

- JWT-based authentication
- Role-based access control (RBAC)
- Permission-based authorization
- Multi-level role hierarchy
- Context-aware permissions (Department/Team level)

### 👥 User Management

- CRUD operations cho users
- User role assignments
- Department và team assignments
- User profile management
- Bulk operations

### 🎭 Role & Permission System

- Hierarchical role system
- Granular permissions
- Permission groups
- Role-permission assignments
- Context-based permissions

### 🏢 Organization Structure

- Department management
- Team management
- Hierarchical organization
- Cross-department permissions

### 📊 Affiliate Marketing System

- Affiliate code generation
- Usage tracking
- Commission calculation
- Performance analytics
- Conversion tracking

### 📝 Audit Logging

- Comprehensive activity logging
- Data change tracking
- Security monitoring
- Compliance reporting
- Performance metrics

### 📁 File Management

- File upload system
- Image processing
- Document management
- Security validation

## 🚀 Cài đặt và chạy

### Yêu cầu hệ thống

- Node.js >= 18
- MySQL >= 8.0
- Yarn hoặc npm

### Cài đặt

1. **Clone repository**

```bash
git clone <repository-url>
cd nh88-CMS-BE
```

2. **Cài đặt dependencies**

```bash
yarn install
```

3. **Cấu hình environment**

```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:

```env
# Database
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=cms_db
DB_SYNCHRONIZE=true

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h

# App
PORT=3000
NODE_ENV=development
```

4. **Chạy database migrations**

```bash
yarn run seed
```

5. **Khởi động ứng dụng**

```bash
# Development
yarn start:dev

# Production
yarn build
yarn start:prod
```

### Scripts có sẵn

```bash
# Development
yarn start:dev          # Chạy với hot reload
yarn start:debug        # Chạy với debug mode

# Production
yarn build              # Build ứng dụng
yarn start:prod         # Chạy production

# Testing
yarn test               # Unit tests
yarn test:e2e           # E2E tests
yarn test:cov           # Coverage report

# Code quality
yarn lint               # ESLint
yarn format             # Prettier

# Database
yarn seed               # Run seeders
```

## 📚 API Documentation

### Base URL

```
http://localhost:3000/api/v1
```

### Authentication

Tất cả API endpoints (trừ auth) yêu cầu JWT token trong header:

```
Authorization: Bearer <jwt_token>
```

### Swagger Documentation

Truy cập: `http://localhost:3000/api-docs`

### API Endpoints

#### Authentication

```
POST /auth/login          # Đăng nhập
POST /auth/register       # Đăng ký
GET  /auth/profile        # Lấy thông tin user hiện tại
```

#### Users

```
GET    /users                    # Lấy danh sách users
POST   /users                    # Tạo user mới
GET    /users/:id                # Lấy thông tin user
PATCH  /users/:id                # Cập nhật user
DELETE /users/:id                # Xóa user
```

#### Roles

```
GET    /roles                    # Lấy danh sách roles
POST   /roles                    # Tạo role mới
GET    /roles/:id                # Lấy thông tin role
PATCH  /roles/:id                # Cập nhật role
DELETE /roles/:id                # Xóa role
```

#### Permissions

```
GET    /permissions              # Lấy danh sách permissions
GET    /permissions/:id          # Lấy thông tin permission
```

#### Departments

```
GET    /departments              # Lấy danh sách departments
POST   /departments              # Tạo department mới
GET    /departments/:id          # Lấy thông tin department
PATCH  /departments/:id          # Cập nhật department
DELETE /departments/:id          # Xóa department
```

#### Teams

```
GET    /teams                    # Lấy danh sách teams
POST   /teams                    # Tạo team mới
GET    /teams/:id                # Lấy thông tin team
PATCH  /teams/:id                # Cập nhật team
DELETE /teams/:id                # Xóa team
```

#### User Role Assignments

```
GET    /user-role-assignments    # Lấy danh sách assignments
POST   /user-role-assignments    # Tạo assignment mới
PATCH  /user-role-assignments/:id # Cập nhật assignment
DELETE /user-role-assignments/:id # Xóa assignment
POST   /user-role-assignments/bulk # Bulk assign roles
```

#### Role Permission Assignments

```
GET    /role-permission-assignments    # Lấy danh sách assignments
POST   /role-permission-assignments    # Tạo assignment mới
PATCH  /role-permission-assignments/:id # Cập nhật assignment
DELETE /role-permission-assignments/:id # Xóa assignment
POST   /role-permission-assignments/bulk # Bulk assign permissions
```

#### Affiliate Management

```
GET    /affiliate                # Lấy danh sách affiliate codes
POST   /affiliate                # Tạo affiliate code mới
GET    /affiliate/:id            # Lấy thông tin affiliate code
PUT    /affiliate/:id            # Cập nhật affiliate code
DELETE /affiliate/:id            # Xóa affiliate code
GET    /affiliate/:id/usage      # Lấy usage history
POST   /affiliate/:id/track      # Track affiliate usage
```

#### Audit Logs

```
GET    /audit-logs               # Query audit logs
GET    /audit-logs/:id           # Lấy thông tin audit log
```

#### File Upload

```
POST   /upload/image             # Upload image
POST   /upload/document          # Upload document
```

## 🗄️ Cấu trúc database

### Entity Relationships

```
User (1) ←→ (N) UserRoleAssignment (N) ←→ (1) Role
User (1) ←→ (N) Affiliate
User (1) ←→ (N) AuditLog

Role (1) ←→ (N) RolePermissionAssignment (N) ←→ (1) Permission
Role (1) ←→ (N) UserRoleAssignment

Permission (1) ←→ (N) RolePermissionAssignment

Department (1) ←→ (N) Team
Department (1) ←→ (N) UserRoleAssignment
Department (1) ←→ (N) RolePermissionAssignment

Team (1) ←→ (N) UserRoleAssignment
Team (1) ←→ (N) RolePermissionAssignment

Affiliate (1) ←→ (N) AffiliateUsage
```

### Core Entities

#### User

- Thông tin cá nhân (username, email, fullName, phone, address)
- Mối quan hệ với roles, departments, teams
- Affiliate codes ownership

#### Role

- Hierarchical role system (SUPER_ADMIN → MANAGER → TEAM_LEADER → EMPLOYEE → USER)
- Permission assignments
- System roles vs custom roles

#### Permission

- Granular permissions (CREATE, READ, UPDATE, DELETE, VIEW, MANAGE, APPROVE, EXPORT, IMPORT)
- Permission groups
- Context-aware permissions

#### Department & Team

- Organizational hierarchy
- Cross-department permissions
- Team-based access control

#### Affiliate

- Affiliate code management
- Commission tracking
- Usage analytics
- Performance metrics

#### AuditLog

- Comprehensive activity logging
- Data change tracking
- Security monitoring
- Compliance reporting

## 🔐 Hệ thống phân quyền

### Role Hierarchy

1. **SUPER_ADMIN** (Level 5)
   - Full system access
   - User management
   - Role management
   - System configuration

2. **MANAGER** (Level 4)
   - Department management
   - Team management
   - User assignments
   - Limited admin access

3. **TEAM_LEADER** (Level 3)
   - Team management
   - Team member assignments
   - Team-specific permissions

4. **EMPLOYEE** (Level 2)
   - Basic system access
   - Limited permissions
   - Department-specific access

5. **USER** (Level 1)
   - Minimal access
   - Basic operations

### Permission Types

- **CREATE**: Tạo mới records
- **READ**: Đọc thông tin
- **UPDATE**: Cập nhật records
- **DELETE**: Xóa records
- **VIEW**: Xem dữ liệu
- **MANAGE**: Quản lý toàn bộ
- **APPROVE**: Phê duyệt operations
- **EXPORT**: Xuất dữ liệu
- **IMPORT**: Nhập dữ liệu

### Context-Aware Permissions

Hệ thống hỗ trợ phân quyền theo context:

- **Global permissions**: Áp dụng toàn hệ thống
- **Department permissions**: Chỉ áp dụng trong department
- **Team permissions**: Chỉ áp dụng trong team

## 📝 Audit Logging

### Audit Actions

- **Data Operations**: CREATE, UPDATE, DELETE, SOFT_DELETE, RESTORE
- **Bulk Operations**: BULK_CREATE, BULK_UPDATE, BULK_DELETE
- **Authentication**: LOGIN, LOGOUT, PASSWORD_CHANGE
- **Authorization**: PERMISSION_CHANGE, ROLE_ASSIGNMENT, ROLE_REMOVAL
- **System**: IMPORT, EXPORT

### Audit Information

- **User Information**: User ID, username, IP address
- **Action Details**: Action type, entity type, entity ID
- **Data Changes**: Old values, new values, changes
- **Context**: Endpoint, HTTP method, request data
- **Performance**: Duration, status, error information
- **Security**: Risk level, approval status

## 🎯 Affiliate System

### Features

- **Code Generation**: Tự động tạo affiliate codes
- **Usage Tracking**: Theo dõi mọi lần sử dụng
- **Commission Calculation**: Tính toán hoa hồng
- **Performance Analytics**: Phân tích hiệu suất
- **Conversion Tracking**: Theo dõi chuyển đổi

### Affiliate Code Properties

- Unique code generation
- Commission rate setting
- Usage limits
- Expiration dates
- Category classification
- Performance metrics

### Usage Tracking

- IP address tracking
- Geographic information
- Device information
- Session tracking
- Conversion metrics
- Commission calculation

## 🚀 Deployment

### Environment Variables

```env
# Application
APP_NAME=CMS Backend
APP_VERSION=1.0.0
PORT=3000
NODE_ENV=production

# Database
DB_HOST=your_db_host
DB_PORT=3306
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password
DB_DATABASE=your_db_name
DB_SYNCHRONIZE=false

# JWT
JWT_SECRET=your_secure_jwt_secret
JWT_EXPIRES_IN=24h

# CORS
CORS_ORIGIN=https://your-frontend-domain.com
CORS_METHODS=GET,POST,PUT,DELETE
CORS_CREDENTIALS=true

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true

# Swagger
SWAGGER_ENABLED=false
```

### Production Deployment

1. **Build application**

```bash
yarn build
```

2. **Set environment variables**

```bash
export NODE_ENV=production
export DB_SYNCHRONIZE=false
export SWAGGER_ENABLED=false
```

3. **Start application**

```bash
yarn start:prod
```

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN yarn install --production

COPY dist ./dist

EXPOSE 3000

CMD ["node", "dist/main"]
```

### PM2 Deployment

```bash
# Install PM2
npm install -g pm2

# Start application
pm2 start dist/main.js --name "cms-backend"

# Monitor
pm2 monit

# Logs
pm2 logs cms-backend
```

## 🧪 Testing

### Unit Tests

```bash
yarn test
```

### E2E Tests

```bash
yarn test:e2e
```

### Coverage Report

```bash
yarn test:cov
```

## 📊 Monitoring & Logging

### Logging Levels

- **ERROR**: Lỗi hệ thống
- **WARN**: Cảnh báo
- **INFO**: Thông tin chung
- **DEBUG**: Debug information

### Log Rotation

- Daily log rotation
- Compressed log files
- Configurable retention

### Performance Monitoring

- Request duration tracking
- Database query monitoring
- Memory usage monitoring
- Error rate tracking

## 🔧 Development

### Code Style

- ESLint configuration
- Prettier formatting
- TypeScript strict mode
- Consistent naming conventions

### Git Workflow

- Feature branches
- Pull request reviews
- Automated testing
- Code quality checks

### Database Migrations

- TypeORM migrations
- Seeder scripts
- Data validation
- Backup strategies

## 📞 Support

### Documentation

- API documentation: `/api-docs`
- Code comments
- README files
- Architecture diagrams

### Troubleshooting

- Log analysis
- Error tracking
- Performance monitoring
- Security auditing

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Maintainer**: NH88 Development Team
