name: Deploy

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Get source
        uses: actions/checkout@v4

      - name: 🔔 Notify start deploy
        run: |
          export TZ='Asia/Ho_Chi_Minh'
          TIME=$(date +'%H:%M:%S %d-%m-%Y')
          ACTOR="${{ github.actor }}"
          curl -s -X POST https://api.telegram.org/bot${{ secrets.NH88_CMS_TELEGRAM_BOT_TOKEN }}/sendMessage \
          -d chat_id=${{ secrets.NH88_CMS_DEPLOY_GROUP_ID }} \
          -d parse_mode=MarkdownV2 \
          -d text="🚀 *Starting Deploy CMS Server*  🕐 Time: \`${TIME}\` 👤 Actor: \`${ACTOR}\`"

      - name: 📦 Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "yarn"

      - name: 📥 Install dependencies
        run: yarn install --frozen-lockfile

      - name: 🛠️ Build
        run: yarn build

      - name: ❌ Notify build failed
        if: failure()
        run: |
          export TZ='Asia/Ho_Chi_Minh'
          TIME=$(date +'%H:%M:%S %d-%m-%Y')
          ACTOR="${{ github.actor }}"
          curl -s -X POST https://api.telegram.org/bot${{ secrets.NH88_CMS_TELEGRAM_BOT_TOKEN }}/sendMessage \
          -d chat_id=${{ secrets.NH88_CMS_DEPLOY_GROUP_ID }} \
          -d parse_mode=MarkdownV2 \
          -d text="❌ *Build CMS Server Failed*  🕐 \`${TIME}\` 👤 \`${ACTOR}\`"

      - name: 🚚 Deploy to VPS
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.NH88_CMS_VPS_HOST }}
          username: ${{ secrets.NH88_CMS_VPS_USER }}
          password: ${{ secrets.NH88_CMS_VPS_PASSWORD }}
          port: 22
          script: |
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            nvm use 22.14.0
            node -v
            yarn -v
            cd /root/www/nh88-CMS-BE
            git fetch origin
            git reset --hard origin/main
            git log -1 > git_commit.log
            rm -rf node_modules dist
            yarn install --frozen-lockfile 2>&1 | tee install.log
            yarn build 2>&1 | tee build.log
            if [ $? -eq 0 ] && [ -f "dist/main.js" ]; then
              pm2 delete nh88-cms-be || true
              pm2 start dist/main.js --name nh88-cms-be
              pm2 save
            else
              echo "Build failed or dist/main.js missing, check logs"
              cat git_commit.log
              cat install.log
              cat build.log
              exit 1
            fi

      - name: ✅ Notify deploy success
        run: |
          export TZ='Asia/Ho_Chi_Minh'
          TIME=$(date +'%H:%M:%S %d-%m-%Y')
          ACTOR="${{ github.actor }}"
          curl -s -X POST https://api.telegram.org/bot${{ secrets.NH88_CMS_TELEGRAM_BOT_TOKEN }}/sendMessage \
          -d chat_id=${{ secrets.NH88_CMS_DEPLOY_GROUP_ID }} \
          -d parse_mode=MarkdownV2 \
          -d text="✅ *Deploy CMS Server Successful* 🎉 CMS Server has been updated 🕐 \`${TIME}\` 👤 \`${ACTOR}\`"

      - name: ❌ Notify deploy failed
        if: failure()
        run: |
          export TZ='Asia/Ho_Chi_Minh'
          TIME=$(date +'%H:%M:%S %d-%m-%Y')
          ACTOR="${{ github.actor }}"
          curl -s -X POST https://api.telegram.org/bot${{ secrets.NH88_CMS_TELEGRAM_BOT_TOKEN }}/sendMessage \
          -d chat_id=${{ secrets.NH88_CMS_DEPLOY_GROUP_ID }} \
          -d parse_mode=MarkdownV2 \
          -d text="🚫 *Deploy CMS Server Failed* 🕐 \`${TIME}\` 👤 \`${ACTOR}\`"
