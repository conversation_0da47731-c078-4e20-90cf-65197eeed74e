# ===========================================
# APPLICATION CONFIGURATION
# ===========================================
NODE_ENV=development
PORT=3000
APP_NAME=CMS Backend
APP_VERSION=1.0.0
APP_DESCRIPTION=Content Management System with RBAC

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
DB_HOST=*************
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=r9bALkmUv3ig
DB_DATABASE=cms_dev
DB_SYNCHRONIZE=true
DB_LOGGING=true
DB_MAX_CONNECTIONS=100
DB_SSL=false

# ===========================================
# JWT CONFIGURATION
# ===========================================
JWT_SECRET=cms-super-secret-jwt-key-2025-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=cms-refresh-secret-key-2025
JWT_REFRESH_EXPIRES_IN=30d

# ===========================================
# CORS CONFIGURATION
# ===========================================
CORS_ORIGIN=http://localhost:5173,http://localhost:3001
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
CORS_CREDENTIALS=true

# ===========================================
# RATE LIMITING
# ===========================================
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# ===========================================
# LOGGING CONFIGURATION
# ===========================================
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true
LOG_MAX_FILES=5
LOG_MAX_SIZE=10m

# ===========================================
# SWAGGER CONFIGURATION
# ===========================================
SWAGGER_ENABLED=true
SWAGGER_PATH=api-docs
SWAGGER_TITLE=CMS API Documentation
SWAGGER_DESCRIPTION=API documentation for Content Management System
SWAGGER_VERSION=1.0.0

# ===========================================
# SECURITY CONFIGURATION
# ===========================================
BCRYPT_ROUNDS=12
SESSION_SECRET=cms-session-secret-2024
HELMET_ENABLED=true

# ===========================================
# FILE UPLOAD CONFIGURATION
# ===========================================
UPLOAD_MAX_SIZE=52428800
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_DEST=./uploads