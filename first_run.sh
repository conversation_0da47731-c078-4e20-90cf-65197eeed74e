#!/bin/bash

set -e

mysql -u "root" -p"" -e "CREATE DATABASE IF NOT EXISTS \`cms_db\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if [ $? -eq 0 ]; then
    echo "Tạo database 'cms_db' thành công (hoặc đã tồn tại)."
else
    echo "Tạo database thất bại."
fi

echo "-------------------------"
echo "1. Build project"
echo "-------------------------"
yarn install
yarn build

echo "-------------------------"
echo "2. Start project"
echo "-------------------------"
yarn start:prod &

sleep 5

echo "-------------------------"
echo "3. Run seeder"
echo "-------------------------"
yarn seed

echo "-------------------------"
echo "Deploy completed!"
echo "-------------------------"
