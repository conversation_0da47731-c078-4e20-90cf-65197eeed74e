{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@entities/*": ["src/entities/*"], "@modules/*": ["src/modules/*"], "@common/*": ["src/common/*"], "@config/*": ["src/config/*"], "@database/*": ["src/database/*"], "@auth/*": ["src/modules/auth/*"], "@users/*": ["src/modules/users/*"], "@roles/*": ["src/modules/roles/*"], "@permissions/*": ["src/modules/permissions/*"], "@groups/*": ["src/modules/groups/*"], "@departments/*": ["src/modules/department/*"], "@teams/*": ["src/modules/team/*"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false}}