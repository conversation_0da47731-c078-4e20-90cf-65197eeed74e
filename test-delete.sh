#!/bin/bash

# Test deleteBulk with both assignment IDs from previous responses
curl -X DELETE \
  'http://localhost:3000/api/v1/user-menu-assignments/bulk' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao' \
  -H 'Content-Type: application/json' \
  -d '{
    "ids": [
      "5a6b253a-2cf6-4131-a04b-3fe57de68190",
      "a0bba34d-c2e4-4e61-a435-235407a1ab96"
    ]
  }'
