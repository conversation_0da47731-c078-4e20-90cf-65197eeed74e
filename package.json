{"name": "cms_be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed": "ts-node -r tsconfig-paths/register src/database/seeders/run-seeder.ts", "seed:menu": "ts-node -r tsconfig-paths/register src/database/seeders/run-menu-seeder.ts", "seed:menu:reset": "ts-node -r tsconfig-paths/register src/database/seeders/reset-menu-seeder.ts", "seed:new-entities": "ts-node -r tsconfig-paths/register src/database/seeders/run-new-entities-seeder.ts"}, "dependencies": {"@nestjs/bull": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@types/bull": "^3.15.9", "@types/xlsx": "^0.0.35", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "ioredis": "^5.6.1", "mysql2": "^3.14.1", "nest-winston": "^1.10.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.25", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.30.1", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^2.0.0", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "eslint": "^9.30.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.3.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.36.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}